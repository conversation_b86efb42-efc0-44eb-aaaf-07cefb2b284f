/**
 * Enhanced AI Agent with advanced analysis capabilities
 * Includes vector embeddings, semantic search, and sophisticated scoring algorithms
 */
export declare const enhancedAnalyzeTweetsBatch: import("convex/server").RegisteredAction<"public", {
    twitterAccountId?: import("convex/values").GenericId<"twitterAccounts"> | undefined;
    userContext?: {
        userId?: import("convex/values").GenericId<"users"> | undefined;
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    limit?: number | undefined;
    analysisOptions?: {
        includeSemanticAnalysis?: boolean | undefined;
        includeTimeDecay?: boolean | undefined;
        customWeights?: {
            contentQuality?: number | undefined;
            authorInfluence?: number | undefined;
            engagementPotential?: number | undefined;
            semanticRelevance?: number | undefined;
            temporalFactor?: number | undefined;
        } | undefined;
        scoringAlgorithm?: "weighted" | "neural" | "hybrid" | undefined;
    } | undefined;
}, Promise<{
    success: boolean;
    processed: number;
    responseWorthy: number;
    averageScore: number;
    results: any[];
    analytics: any;
    error?: string;
}>>;
export declare const calculateAdvancedScore: import("convex/server").RegisteredAction<"public", {
    contextData?: {
        userInterests?: string | undefined;
        semanticSimilarity?: number | undefined;
    } | undefined;
    tweetId: string;
    content: string;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    authorData: {
        followerCount?: number | undefined;
        isVerified?: boolean | undefined;
        accountAge?: number | undefined;
        handle: string;
    };
}, Promise<{
    tweetId: string;
    finalScore: number;
    componentScores: any;
    algorithm: string;
    metadata: {
        confidence: number;
        reasoning: string[];
        recommendations: string[];
    };
    calculatedAt: number;
}>>;
