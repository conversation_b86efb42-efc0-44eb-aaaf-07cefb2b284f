import { mutation, query } from "../_generated/server";
import { v } from "convex/values";
import nacl from 'tweetnacl';
import bs58 from 'bs58';
import { TextEncoder } from 'util';

/**
 * Initiate wallet verification process
 * Creates a challenge that the user must sign to prove wallet ownership
 */
export const initiateWalletVerification = mutation({
  args: {
    address: v.string(),
    blockchain: v.union(v.literal("ethereum"), v.literal("solana"), v.literal("polygon"), v.literal("base")),
    walletType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Validate address format
    if (!isValidAddress(args.address, args.blockchain)) {
      throw new Error("Invalid wallet address format");
    }

    // Check if wallet already exists and is verified
    const existingWallet = await ctx.db
      .query("wallets")
      .withIndex("by_address", (q) => q.eq("address", args.address))
      .first();

    if (existingWallet && existingWallet.verified) {
      throw new Error("Wallet already verified");
    }

    // Clean up any expired verification attempts
    await cleanupExpiredVerifications(ctx);

    // Generate unique challenge message
    const challenge = generateVerificationChallenge(user.name, args.address);

    // Store pending verification
    const verificationId = await ctx.db.insert("walletVerifications", {
      userId: user._id,
      address: args.address,
      blockchain: args.blockchain,
      challenge,
      status: "pending",
      expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
      createdAt: Date.now(),
    });

    return {
      verificationId,
      challenge,
      expiresAt: Date.now() + 5 * 60 * 1000,
      blockchain: args.blockchain,
    };
  },
});

/**
 * Complete wallet verification with signature
 */
export const verifyWalletSignature = mutation({
  args: {
    verificationId: v.id("walletVerifications"),
    signature: v.string(),
    walletType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Get verification record
    const verification = await ctx.db.get(args.verificationId);
    if (!verification || verification.userId !== user._id) {
      throw new Error("Verification not found");
    }

    // Check if verification has expired
    if (verification.expiresAt < Date.now()) {
      await ctx.db.patch(verification._id, { status: "expired" });
      throw new Error("Verification expired");
    }

    // Check if already verified
    if (verification.status !== "pending") {
      throw new Error("Verification already processed");
    }

    // Update verification attempt
    await ctx.db.patch(verification._id, {
      signature: args.signature,
      attemptedAt: Date.now(),
    });

    // Verify signature (simplified - in production, use proper crypto libraries)
    const isValidSignature = await verifySignature(
      verification.challenge,
      args.signature,
      verification.address,
      verification.blockchain
    );

    if (!isValidSignature) {
      await ctx.db.patch(verification._id, { status: "failed" });
      throw new Error("Invalid signature");
    }

    // Mark verification as successful
    await ctx.db.patch(verification._id, { status: "verified" });

    // Check if wallet already exists
    const existingWallet = await ctx.db
      .query("wallets")
      .withIndex("by_address", (q) => q.eq("address", verification.address))
      .first();

    let walletId;
    if (existingWallet) {
      // Update existing wallet
      await ctx.db.patch(existingWallet._id, {
        verified: true,
        connectedVia: "manual",
        walletType: args.walletType || existingWallet.walletType,
        lastUsedAt: Date.now(),
      });
      walletId = existingWallet._id;
    } else {
      // Create new verified wallet
      walletId = await ctx.db.insert("wallets", {
        userId: user._id,
        address: verification.address,
        blockchain: verification.blockchain,
        walletType: args.walletType || "unknown",
        verified: true,
        isPrimary: false, // Will be set if user chooses
        connectedVia: "manual",
        connectedAt: Date.now(),
        lastUsedAt: Date.now(),
      });

      // Set as primary if user has no primary wallet
      const userWallets = await ctx.db
        .query("wallets")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .collect();

      const hasPrimary = userWallets.some(w => w.isPrimary);
      if (!hasPrimary) {
        await ctx.db.patch(walletId, { isPrimary: true });
        await ctx.db.patch(user._id, { primaryWalletId: walletId });
      }
    }

    const wallet = await ctx.db.get(walletId);
    return {
      success: true,
      wallet,
    };
  },
});

/**
 * Get pending verification for user
 */
export const getPendingVerification = query({
  args: {
    address: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return null;
    }

    let query = ctx.db
      .query("walletVerifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id));

    const verifications = await query.collect();

    return verifications
      .filter(v => 
        v.status === "pending" && 
        v.expiresAt > Date.now() &&
        (!args.address || v.address === args.address)
      )
      .sort((a, b) => b.createdAt - a.createdAt)[0] || null;
  },
});

/**
 * Cancel pending verification
 */
export const cancelVerification = mutation({
  args: {
    verificationId: v.id("walletVerifications"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const verification = await ctx.db.get(args.verificationId);
    if (!verification || verification.userId !== user._id) {
      throw new Error("Verification not found");
    }

    await ctx.db.patch(verification._id, { status: "failed" });
    return { success: true };
  },
});

// Utility functions

/**
 * Generate a unique challenge message for wallet verification
 */
function generateVerificationChallenge(userName: string, address: string): string {
  const timestamp = Date.now();
  const nonce = Math.random().toString(36).substring(2, 15);
  
  return `BuddyChip Wallet Verification

I am ${userName} and I want to connect this wallet to my BuddyChip account.

Wallet: ${address}
Timestamp: ${timestamp}
Nonce: ${nonce}

This request will not trigger any blockchain transaction or cost any gas fees.`;
}

/**
 * Validate wallet address format
 */
function isValidAddress(address: string, blockchain: string): boolean {
  switch (blockchain) {
    case "ethereum":
    case "polygon":
    case "base":
      // EVM address validation
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    
    case "solana":
      // Solana address validation (simplified)
      return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address) && address.length >= 32 && address.length <= 44;
    
    default:
      return false;
  }
}

/**
 * Verify signature
 * In production, use proper cryptographic libraries
 */
async function verifySignature(
  message: string,
  signatureString: string,
  addressString: string,
  blockchain: string
): Promise<boolean> {
  if (blockchain === "solana") {
    if (!signatureString || signatureString.length < 64) {
      // Basic check, actual length can vary but should be substantial
      console.warn("Solana signature is too short or missing.");
      return false;
    }
    try {
      const messageBytes = new TextEncoder().encode(message);
      const signatureBytes = bs58.decode(signatureString);
      const publicKeyBytes = bs58.decode(addressString);

      // Ensure public key is the expected length for Ed25519 (32 bytes)
      if (publicKeyBytes.length !== 32) {
        console.warn(`Decoded public key has incorrect length: ${publicKeyBytes.length}`);
        return false;
      }

      return nacl.sign.detached.verify(messageBytes, signatureBytes, publicKeyBytes);
    } catch (error) {
      console.error("Error verifying Solana signature:", error);
      return false;
    }
  } else if (blockchain === "ethereum" || blockchain === "polygon" || blockchain === "base") {
    // TODO: Implement actual signature verification for EVM chains
    // For Ethereum: use ethers.verifyMessage
    console.warn(`Signature verification for ${blockchain} is not yet implemented.`);
    return true; // Placeholder for EVM, assuming valid for now as per original code
  } else {
    console.warn(`Unsupported blockchain for signature verification: ${blockchain}`);
    return false;
  }
}

/**
 * Clean up expired verification attempts
 */
async function cleanupExpiredVerifications(ctx: any) {
  const expiredVerifications = await ctx.db
    .query("walletVerifications")
    .withIndex("by_expires", (q: any) => q.lt("expiresAt", Date.now()))
    .collect();

  for (const verification of expiredVerifications) {
    if (verification.status === "pending") {
      await ctx.db.patch(verification._id, { status: "expired" });
    }
  }
}