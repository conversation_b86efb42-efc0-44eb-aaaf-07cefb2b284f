/**
 * Get test users for health checks (ADMIN ONLY)
 * 🔐 SECURITY: This function should only be accessible by administrators
 */
export declare const getTestUsers: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"users">;
    name: string;
    email: string;
    _creationTime: number;
}[]>>;
/**
 * Get active Twitter accounts for current user only
 * 🔐 SECURITY: Only returns accounts owned by authenticated user
 */
export declare const getActiveTwitterAccounts: import("convex/server").RegisteredQuery<"public", {}, Promise<{
    _id: import("convex/values").GenericId<"twitterAccounts">;
    _creationTime: number;
    updatedAt?: number | undefined;
    isMonitoringEnabled?: boolean | undefined;
    lastScrapedAt?: number | undefined;
    createdAt: number;
    userId: import("convex/values").GenericId<"users">;
    handle: string;
    displayName: string;
    isActive: boolean;
}[]>>;
/**
 * Get user settings for authenticated user only
 * 🔐 SECURITY: Users can only access their own settings
 */
export declare const getUserSettings: import("convex/server").RegisteredQuery<"public", {
    userId?: import("convex/values").GenericId<"users"> | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"userSettings">;
    _creationTime: number;
    preferredResponseStyle?: string | undefined;
    autoApproveResponses?: boolean | undefined;
    notificationPreferences?: {
        emailEnabled: boolean;
        pushEnabled: boolean;
        priorityOnly: boolean;
    } | undefined;
    analysisPreferences?: {
        enableSemanticAnalysis: boolean;
        minEngagementThreshold: number;
        keywordFilters: string[];
    } | undefined;
    apiPreferences?: {
        preferredModel: string;
        maxTokensPerRequest: number;
        temperatureSetting: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
} | null>>;
/**
 * Get user by Clerk ID (INTERNAL USE ONLY)
 * 🔐 SECURITY: This function should only be used internally by the system
 */
export declare const getUserByClerkId: import("convex/server").RegisteredQuery<"public", {
    clerkId: string;
}, Promise<{
    _id: import("convex/values").GenericId<"users">;
    _creationTime: number;
    image?: string | undefined;
    primaryWalletId?: import("convex/values").GenericId<"wallets"> | undefined;
    walletPreferences?: {
        preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
        autoConnectWallet?: boolean | undefined;
        showBalances?: boolean | undefined;
    } | undefined;
    lastMentionRefresh?: number | undefined;
    updatedAt?: number | undefined;
    name: string;
    email: string;
    clerkId: string;
    createdAt: number;
} | null>>;
/**
 * Get current authenticated user
 */
export declare const getCurrentUser: import("convex/server").RegisteredQuery<"public", {}, Promise<{
    _id: import("convex/values").GenericId<"users">;
    _creationTime: number;
    image?: string | undefined;
    primaryWalletId?: import("convex/values").GenericId<"wallets"> | undefined;
    walletPreferences?: {
        preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
        autoConnectWallet?: boolean | undefined;
        showBalances?: boolean | undefined;
    } | undefined;
    lastMentionRefresh?: number | undefined;
    updatedAt?: number | undefined;
    name: string;
    email: string;
    clerkId: string;
    createdAt: number;
} | null>>;
/**
 * Get tweet by ID (user's tweets only)
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export declare const getTweetById: import("convex/server").RegisteredQuery<"public", {
    tweetId: import("convex/values").GenericId<"tweets">;
}, Promise<{
    _id: import("convex/values").GenericId<"tweets">;
    _creationTime: number;
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    scrapedAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
} | null>>;
/**
 * Get mention by ID (user's mentions only)
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export declare const getMentionById: import("convex/server").RegisteredQuery<"public", {
    mentionId: import("convex/values").GenericId<"mentions">;
}, Promise<{
    _id: import("convex/values").GenericId<"mentions">;
    _creationTime: number;
    processedAt?: number | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    aiAnalysisResult?: {
        responseStrategy?: string | undefined;
        sentiment?: string | undefined;
        topics?: string[] | undefined;
        confidence?: number | undefined;
        shouldRespond: boolean;
    } | undefined;
    sentimentAnalysis?: {
        emotions?: {
            excitement: number;
            fear: number;
            greed: number;
            fomo: number;
            panic: number;
        } | undefined;
        sentiment: "bullish" | "bearish" | "neutral";
        confidence: number;
        sentimentScore: number;
        marketSentiment: {
            bullishScore: number;
            bearishScore: number;
            neutralScore: number;
            marketContext: string[];
        };
        reasoning: string;
        keyWords: string[];
        analysisModel: string;
        analyzedAt: number;
    } | undefined;
    notificationSentAt?: number | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    priority: "high" | "medium" | "low";
    isProcessed: boolean;
    isNotificationSent: boolean;
    discoveredAt: number;
} | null>>;
/**
 * Get response by ID (user's responses only)
 * 🔐 SECURITY: Users can only access their own responses
 */
export declare const getResponseById: import("convex/server").RegisteredQuery<"public", {
    responseId: import("convex/values").GenericId<"responses">;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
} | null>>;
/**
 * Get Twitter account by handle (user's accounts only)
 * 🔐 SECURITY: Users can only access their own Twitter accounts
 */
export declare const getTwitterAccountByHandle: import("convex/server").RegisteredQuery<"public", {
    handle: string;
}, Promise<{
    _id: import("convex/values").GenericId<"twitterAccounts">;
    _creationTime: number;
    updatedAt?: number | undefined;
    isMonitoringEnabled?: boolean | undefined;
    lastScrapedAt?: number | undefined;
    createdAt: number;
    userId: import("convex/values").GenericId<"users">;
    handle: string;
    displayName: string;
    isActive: boolean;
} | null>>;
/**
 * Get unprocessed mentions for current user only
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export declare const getUnprocessedMentions: import("convex/server").RegisteredQuery<"public", {
    monitoredAccountId?: import("convex/values").GenericId<"twitterAccounts"> | undefined;
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"mentions">;
    _creationTime: number;
    processedAt?: number | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    aiAnalysisResult?: {
        responseStrategy?: string | undefined;
        sentiment?: string | undefined;
        topics?: string[] | undefined;
        confidence?: number | undefined;
        shouldRespond: boolean;
    } | undefined;
    sentimentAnalysis?: {
        emotions?: {
            excitement: number;
            fear: number;
            greed: number;
            fomo: number;
            panic: number;
        } | undefined;
        sentiment: "bullish" | "bearish" | "neutral";
        confidence: number;
        sentimentScore: number;
        marketSentiment: {
            bullishScore: number;
            bearishScore: number;
            neutralScore: number;
            marketContext: string[];
        };
        reasoning: string;
        keyWords: string[];
        analysisModel: string;
        analyzedAt: number;
    } | undefined;
    notificationSentAt?: number | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    priority: "high" | "medium" | "low";
    isProcessed: boolean;
    isNotificationSent: boolean;
    discoveredAt: number;
}[]>>;
/**
 * Get tweets by account IDs (user's accounts only)
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export declare const getTweetsByAccountIds: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
    accountIds: import("convex/values").GenericId<"twitterAccounts">[];
}, Promise<{
    _id: import("convex/values").GenericId<"tweets">;
    _creationTime: number;
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    scrapedAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
}[]>>;
/**
 * Get mentions by account IDs (user's accounts only)
 * 🔐 SECURITY: Users can only access mentions for their own accounts
 */
export declare const getMentionsByAccountIds: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
    accountIds: import("convex/values").GenericId<"twitterAccounts">[];
}, Promise<{
    _id: import("convex/values").GenericId<"mentions">;
    _creationTime: number;
    processedAt?: number | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    mentionAuthorFollowers?: number | undefined;
    mentionAuthorVerified?: boolean | undefined;
    originalTweetId?: string | undefined;
    aiAnalysisResult?: {
        responseStrategy?: string | undefined;
        sentiment?: string | undefined;
        topics?: string[] | undefined;
        confidence?: number | undefined;
        shouldRespond: boolean;
    } | undefined;
    sentimentAnalysis?: {
        emotions?: {
            excitement: number;
            fear: number;
            greed: number;
            fomo: number;
            panic: number;
        } | undefined;
        sentiment: "bullish" | "bearish" | "neutral";
        confidence: number;
        sentimentScore: number;
        marketSentiment: {
            bullishScore: number;
            bearishScore: number;
            neutralScore: number;
            marketContext: string[];
        };
        reasoning: string;
        keyWords: string[];
        analysisModel: string;
        analyzedAt: number;
    } | undefined;
    notificationSentAt?: number | undefined;
    createdAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionTweetId: string;
    mentionContent: string;
    mentionAuthor: string;
    mentionAuthorHandle: string;
    monitoredAccountId: import("convex/values").GenericId<"twitterAccounts">;
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    priority: "high" | "medium" | "low";
    isProcessed: boolean;
    isNotificationSent: boolean;
    discoveredAt: number;
}[]>>;
/**
 * Get all responses for authenticated user only
 * 🔐 SECURITY: Users can only access their own responses
 */
export declare const getAllResponses: import("convex/server").RegisteredQuery<"public", {
    userId?: import("convex/values").GenericId<"users"> | undefined;
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"responses">;
    _creationTime: number;
    responseStrategy?: string | undefined;
    generationModel?: string | undefined;
    contextUsed?: string[] | undefined;
    estimatedEngagement?: {
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    generatedImage?: string | undefined;
    imagePrompt?: string | undefined;
    isEnhanced?: boolean | undefined;
    postedAt?: number | undefined;
    postedTweetId?: string | undefined;
    actualEngagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    approvedAt?: number | undefined;
    userFeedback?: {
        notes?: string | undefined;
        rating: number;
    } | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    status: "failed" | "draft" | "approved" | "declined" | "posted";
    content: string;
    confidence: number;
    targetType: "mention" | "tweet";
    targetId: import("convex/values").GenericId<"tweets"> | import("convex/values").GenericId<"mentions">;
    style: string;
    characterCount: number;
}[]>>;
export declare const getUserEmbeddings: import("convex/server").RegisteredQuery<"public", {
    userId: import("convex/values").GenericId<"users">;
    limit: number;
}, Promise<never[]>>;
export declare const getAllTweetEmbeddings: import("convex/server").RegisteredQuery<"public", {
    limit: number;
}, Promise<never[]>>;
export declare const getAllMentionEmbeddings: import("convex/server").RegisteredQuery<"public", {
    limit: number;
}, Promise<never[]>>;
export declare const getAllResponseEmbeddings: import("convex/server").RegisteredQuery<"public", {
    limit: number;
}, Promise<never[]>>;
/**
 * Get all tweets for authenticated user only
 * 🔐 SECURITY: Users can only access tweets from their own accounts
 */
export declare const getAllTweets: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"tweets">;
    _creationTime: number;
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    scrapedAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
}[]>>;
