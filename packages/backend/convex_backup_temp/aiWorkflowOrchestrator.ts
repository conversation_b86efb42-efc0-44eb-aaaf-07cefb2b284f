import { action, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";


/**
 * Enhanced AI Analysis Workflow Orchestrator
 * Coordinates the complete AI analysis pipeline with vector embeddings and advanced scoring
 */

export const runEnhancedAnalysisWorkflow = action({
  args: {
    tweetIds: v.array(v.id("tweets")),
    userId: v.id("users"),
    options: v.optional(v.object({
      includeEmbeddings: v.optional(v.boolean()),
      enhancedScoring: v.optional(v.boolean()),
      useSemanticContext: v.optional(v.boolean()),
      batchSize: v.optional(v.number()),
      prioritizeRecent: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args) => {
    try {
      const options = {
        includeEmbeddings: true,
        enhancedScoring: true,
        useSemanticContext: true,
        batchSize: 10,
        prioritizeRecent: true,
        ...args.options,
      };

      const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Initialize workflow status
      await ctx.runMutation(api.aiWorkflowOrchestrator.createWorkflowStatus, {
        workflowId,
        totalTweets: args.tweetIds.length,
        userId: args.userId,
        options,
      });

      let processedCount = 0;
      const results = [];
      const errors = [];

      // Step 1: Generate embeddings for tweets that don't have them
      if (options.includeEmbeddings) {
        try {
          await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
            workflowId,
            step: "generating_embeddings",
            progress: 0,
          });

          const embeddingResult = await ctx.runAction(api.aiAgentEnhanced.batchProcessEmbeddingsOptimized, {
            tweetIds: args.tweetIds,
            options: {
              overwriteExisting: false,
              batchSize: options.batchSize,
              prioritizeRecent: options.prioritizeRecent,
              maxConcurrency: 3,
            },
          });

          await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
            workflowId,
            step: "embeddings_generated",
            progress: 25,
            metadata: {
              embeddingsGenerated: embeddingResult.processed,
              embeddingSuccessRate: embeddingResult.performance?.successRate,
            },
          });
        } catch (embeddingError) {
          console.warn('Embedding generation failed:', embeddingError);
          errors.push({
            step: 'embedding_generation',
            error: embeddingError instanceof Error ? embeddingError.message : 'Unknown error',
          });
        }
      }

      // Step 2: Get user context for semantic analysis
      let userContext = null;
      if (options.useSemanticContext) {
        try {
          userContext = await ctx.runQuery(api.embeddings.embeddingQueries.getUserContextEmbeddings, {
            userId: args.userId,
            limit: 10,
          });

          await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
            workflowId,
            step: "user_context_loaded",
            progress: 35,
            metadata: {
              userContextItems: userContext.length,
            },
          });
        } catch (contextError) {
          console.warn('User context loading failed:', contextError);
          errors.push({
            step: 'user_context_loading',
            error: contextError instanceof Error ? contextError.message : 'Unknown error',
          });
        }
      }

      // Step 3: Batch analyze tweets with enhanced algorithms
      await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
        workflowId,
        step: "analyzing_tweets",
        progress: 40,
      });

      // Get tweet data for analysis
      const tweets = await Promise.all(
        args.tweetIds.map(async (tweetId) => {
          const tweet = await ctx.runQuery(api.tweets.getTweetById, { tweetId });
          return tweet ? { 
            id: tweetId, 
            ...tweet,
            createdAt: tweet.createdAt || Date.now(),
          } : null;
        })
      );

      const validTweets = tweets.filter(Boolean);

      // Run enhanced batch analysis
      try {
        const analysisResult: { results: any[]; analyzed: number; insights: any } = await ctx.runAction(api.aiAgent.analyzeTweetsBatchEnhanced, {
          tweets: validTweets.map((tweet: any) => ({
            id: tweet._id,
            content: tweet.content,
            author: tweet.author,
            authorHandle: tweet.authorHandle,
            authorIsVerified: tweet.authorIsVerified,
            authorFollowerCount: tweet.authorFollowerCount,
            engagement: tweet.engagement,
            createdAt: tweet.createdAt,
          })),
          userContext: {
            userId: args.userId,
            // Add other user context as needed
          },
          options: {
            includeEmbeddings: options.includeEmbeddings,
            enhancedScoring: options.enhancedScoring,
            batchSize: options.batchSize,
            useSemanticContext: options.useSemanticContext,
          },
        });

        results.push(...analysisResult.results);
        processedCount = analysisResult.analyzed;

        await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
          workflowId,
          step: "tweets_analyzed",
          progress: 75,
          metadata: {
            analysisInsights: analysisResult.insights,
            processedCount,
          },
        });
      } catch (analysisError) {
        console.error('Batch analysis failed:', analysisError);
        errors.push({
          step: 'batch_analysis',
          error: analysisError instanceof Error ? analysisError.message : 'Unknown error',
        });
      }

      // Step 4: Update tweet records with analysis results
      if (results.length > 0) {
        await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
          workflowId,
          step: "updating_records",
          progress: 85,
        });

        const updatePromises = results
          .filter(result => result.success && result.analysis)
          .map(async (result) => {
            try {
              await ctx.runMutation(api.tweets.updateTweetAnalysis, {
                tweetId: result.tweetId,
                analysisStatus: result.analysis.shouldRespond ? "response_worthy" : "analyzed",
                analysisScore: result.enhancedScore || result.analysis.confidence || 0,
                analysisReason: result.analysis.reasons?.join('; ') || 'Analyzed',
                embeddingId: result.embedding ? "generated" : undefined,
              });
              return { tweetId: result.tweetId, success: true };
            } catch (updateError) {
              console.error(`Failed to update tweet ${result.tweetId}:`, updateError);
              return {
                tweetId: result.tweetId,
                success: false,
                error: updateError instanceof Error ? updateError.message : 'Unknown error',
              };
            }
          });

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(r => r.success).length;

        await ctx.runMutation(api.aiWorkflowOrchestrator.updateWorkflowStatus, {
          workflowId,
          step: "records_updated",
          progress: 95,
          metadata: {
            updatedRecords: successfulUpdates,
          },
        });
      }

      // Step 5: Complete workflow
      const workflowSummary = {
        totalTweets: args.tweetIds.length,
        processed: processedCount,
        responseWorthy: results.filter(r => r.success && r.analysis?.shouldRespond).length,
        errors: errors.length,
        averageScore: results.length > 0 
          ? results.reduce((sum, r) => sum + (r.enhancedScore || r.analysis?.confidence || 0), 0) / results.length
          : 0,
      };

      await ctx.runMutation(api.aiWorkflowOrchestrator.completeWorkflow, {
        workflowId,
        summary: workflowSummary,
        results: results.slice(0, 10), // Store only first 10 results to avoid size limits
        errors,
      });

      return {
        workflowId,
        success: true,
        summary: workflowSummary,
        errors,
        completedAt: Date.now(),
      };
    } catch (error) {
      console.error('Enhanced analysis workflow failed:', error);
      throw new Error(`Workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Semantic content discovery workflow
 */
export const runSemanticDiscoveryWorkflow = action({
  args: {
    query: v.string(),
    userId: v.id("users"),
    options: v.optional(v.object({
      includeUserContext: v.optional(v.boolean()),
      minSimilarity: v.optional(v.number()),
      maxResults: v.optional(v.number()),
      timeRange: v.optional(v.object({
        start: v.number(),
        end: v.number(),
      })),
    })),
  },
  handler: async (ctx, args): Promise<{
    query: string;
    results: any | null;
    insights: any | null;
    userContext: any | null;
    opportunities: any | null;
    summary: any | null;
    discoveredAt: number | null;
  }> => {
    try {
      const options = {
        includeUserContext: true,
        minSimilarity: 0.7,
        maxResults: 20,
        ...args.options,
      };

      // Step 1: Semantic search for similar tweets
              const semanticResults: { results: any[]; insights: any } = await ctx.runAction(api.aiAgentEnhanced.semanticSearchTweets, {
        query: args.query,
        userId: args.userId,
        limit: options.maxResults,
        minSimilarity: options.minSimilarity,
        filters: {
          timeRange: options.timeRange,
        },
      });

      // Step 2: Get user context if requested
      let userContext = null;
      if (options.includeUserContext) {
        try {
          const contextResult = await ctx.runAction(api.embeddings.embeddingQueries.findRelevantContext, {
            query: args.query,
            limit: 5,
          });
          userContext = contextResult.contexts;
        } catch (contextError) {
          console.warn('Failed to get user context:', contextError);
        }
      }

      // Step 3: Analyze discovered content for opportunities
      const opportunities = [];
      if (semanticResults.results.length > 0) {
        for (const result of semanticResults.results.slice(0, 5)) {
          try {
            // Run enhanced analysis on discovered content
            const analysis = await ctx.runAction(api.aiAgent.analyzeSingleTweetEnhanced, {
              tweetId: result.tweetId,
              content: `Semantic discovery for: ${args.query}`,
              author: "Discovery",
              options: {
                enhancedScoring: true,
                generateSemanticInsights: true,
              },
            });

            if (analysis.analysis?.worthiness?.shouldRespond) {
              opportunities.push({
                ...result,
                analysis: analysis.analysis,
                discoveryRelevance: result.similarity,
              });
            }
          } catch (analysisError) {
            console.warn('Failed to analyze discovered content:', analysisError);
          }
        }
      }

      return {
        query: args.query,
        results: semanticResults.results,
        insights: semanticResults.insights,
        userContext,
        opportunities,
        summary: {
          totalFound: semanticResults.results.length,
          opportunities: opportunities.length,
          averageSimilarity: semanticResults.insights.averageSimilarity,
        },
        discoveredAt: Date.now(),
      };
    } catch (error) {
      console.error('Semantic discovery workflow failed:', error);
      throw new Error(`Semantic discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Workflow status management
 */
export const createWorkflowStatus = mutation({
  args: {
    workflowId: v.string(),
    totalTweets: v.number(),
    userId: v.id("users"),
    options: v.any(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("workflowStatus", {
      workflowId: args.workflowId,
      userId: args.userId,
      totalTweets: args.totalTweets,
      step: "initialized",
      progress: 0,
      options: args.options,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const updateWorkflowStatus = mutation({
  args: {
    workflowId: v.string(),
    step: v.string(),
    progress: v.number(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("workflowStatus")
      .filter((q) => q.eq(q.field("workflowId"), args.workflowId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        step: args.step,
        progress: args.progress,
        metadata: args.metadata,
        updatedAt: Date.now(),
      });
    }
  },
});

export const completeWorkflow = mutation({
  args: {
    workflowId: v.string(),
    summary: v.any(),
    results: v.array(v.any()),
    errors: v.array(v.any()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("workflowStatus")
      .filter((q) => q.eq(q.field("workflowId"), args.workflowId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        step: "completed",
        progress: 100,
        summary: args.summary,
        results: args.results,
        errors: args.errors,
        completedAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

export const getWorkflowStatus = query({
  args: {
    workflowId: v.string(),
  },
  handler: async (ctx, args) => {
    const workflow = await ctx.db
      .query("workflowStatus")
      .filter((q) => q.eq(q.field("workflowId"), args.workflowId))
      .first();

    return workflow;
  },
});

export const getUserWorkflows = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const workflows = await ctx.db
      .query("workflowStatus")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .order("desc")
      .take(args.limit || 10);

    return workflows;
  },
});