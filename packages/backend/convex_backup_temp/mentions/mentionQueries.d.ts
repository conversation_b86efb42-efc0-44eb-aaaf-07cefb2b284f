export declare const getMentionStats: any;
export declare const getRecentMentions: any;
export declare const getMentionResponses: any;
export declare const getUnreadMentions: any;
export declare const getHighPriorityMentions: any;
export declare const getMentionsByType: any;
export declare const searchMentions: any;
export declare const getMentionEngagementStats: any;
export declare const getUnprocessedMentions: any;
export declare const getUserMentionStats: any;
/**
 * Get the latest mention for a specific account
 * Used to determine the last time we checked for mentions
 */
export declare const getLatestMentionForAccount: any;
/**
 * Get a mention by its Twitter tweet ID
 * Used to check for duplicates before storing new mentions
 */
export declare const getMentionByTweetId: any;
/**
 * Get mention by ID with all details
 * Used by AI processing functions
 */
export declare const getMentionById: any;
/**
 * Development function to test mention retrieval system
 * Note: This function is for development/debugging purposes only
 */
export declare const testMentionRetrieval: any;
/**
 * Get mentions for sentiment analysis (no authentication required)
 * Used for batch processing sentiment analysis
 */
export declare const getMentionsForSentimentAnalysis: any;
/**
 * Get a Twitter account by ID (no authentication required)
 * Used for sentiment analysis processing
 */
export declare const getTwitterAccountById: any;
/**
 * TEMPORARY: Bypass authentication to get recent mentions
 * This bypasses auth issues to show mentions in the UI
 */
export declare const getRecentMentionsNoAuth: any;
/**
 * TEMPORARY: Get user stats without authentication
 */
export declare const getUserStatsNoAuth: any;
/**
 * Get unprocessed mentions for sentiment analysis (internal function)
 */
export declare const getUnprocessedMentionsForSentiment: any;
