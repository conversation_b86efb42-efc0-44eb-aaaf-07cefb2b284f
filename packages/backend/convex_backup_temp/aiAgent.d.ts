/**
 * Enhanced batch tweet analysis with improved algorithms and vector embedding support
 */
export declare const analyzeTweetsBatchEnhanced: import("convex/server").RegisteredAction<"public", {
    options?: {
        batchSize?: number | undefined;
        includeEmbeddings?: boolean | undefined;
        enhancedScoring?: boolean | undefined;
        useSemanticContext?: boolean | undefined;
    } | undefined;
    userContext?: {
        userId?: import("convex/values").GenericId<"users"> | undefined;
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    tweets: {
        createdAt?: number | undefined;
        authorHandle?: string | undefined;
        engagement?: {
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        } | undefined;
        authorIsVerified?: boolean | undefined;
        authorFollowerCount?: number | undefined;
        id: string;
        content: string;
        author: string;
    }[];
}, Promise<{
    analyzed: number;
    total: number;
    results: any[];
    insights: any;
    analyzedAt: number;
}>>;
/**
 * Legacy batch analysis for backward compatibility
 */
export declare const analyzeTweetsBatch: import("convex/server").RegisteredAction<"public", {
    userContext?: {
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    tweets: {
        authorHandle?: string | undefined;
        engagement?: {
            views?: number | undefined;
            likes: number;
            retweets: number;
            replies: number;
        } | undefined;
        authorIsVerified?: boolean | undefined;
        authorFollowerCount?: number | undefined;
        id: string;
        content: string;
        author: string;
    }[];
}, Promise<{
    analyzed: number;
    total: number;
    results: any[];
    insights: any;
    analyzedAt: number;
}>>;
/**
 * Enhanced comprehensive analysis for a single tweet with vector embeddings
 */
export declare const analyzeSingleTweetEnhanced: import("convex/server").RegisteredAction<"public", {
    createdAt?: number | undefined;
    authorHandle?: string | undefined;
    engagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    options?: {
        enhancedScoring?: boolean | undefined;
        includeEmbedding?: boolean | undefined;
        findSimilarContent?: boolean | undefined;
        generateSemanticInsights?: boolean | undefined;
    } | undefined;
    userContext?: {
        userId?: import("convex/values").GenericId<"users"> | undefined;
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    authorIsVerified?: boolean | undefined;
    authorFollowerCount?: number | undefined;
    tweetId: string;
    content: string;
    author: string;
}, Promise<{
    tweetId: string;
    analysis: any;
    embedding: number[] | null;
    similarContent: any[];
    semanticRelevance: number;
    enhancedScore: number | undefined;
    analyzedAt: number;
}>>;
/**
 * Legacy single tweet analysis for backward compatibility
 */
export declare const analyzeSingleTweet: import("convex/server").RegisteredAction<"public", {
    authorHandle?: string | undefined;
    engagement?: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    } | undefined;
    userContext?: {
        expertise?: string[] | undefined;
        interests?: string[] | undefined;
        brand?: string | undefined;
    } | undefined;
    authorIsVerified?: boolean | undefined;
    authorFollowerCount?: number | undefined;
    tweetId: string;
    content: string;
    author: string;
}, Promise<{
    tweetId: string;
    analysis: any;
    analyzedAt: number;
}>>;
