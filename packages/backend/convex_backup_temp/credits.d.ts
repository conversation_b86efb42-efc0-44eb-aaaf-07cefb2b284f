/**
 * Get the credit balance for the currently authenticated user.
 */
export declare const getCredits: import("convex/server").RegisteredQuery<"public", {}, Promise<{
    balance: number;
}>>;
/**
 * Purchase credits with SPL tokens.
 * Verifies a Solana transaction and updates credit balance.
 */
export declare const purchaseCredits: import("convex/server").RegisteredMutation<"public", {
    splTokenAmount: number;
    transactionSignature: string;
    creditsToPurchase: number;
}, Promise<{
    success: boolean;
    newBalance: number;
}>>;
