import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
import { Id } from "../_generated/dataModel";

/**
 * Get user's current subscription
 */
export const getUserSubscription = query({
  args: {},
  handler: async (ctx) => {
    console.log("🔍 Getting user subscription...");
    
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      console.log("❌ No user identity found");
      throw new Error("User not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      console.log("❌ User not found in database");
      throw new Error("User not found");
    }

    console.log(`✅ Found user: ${user.name} (${user._id})`);

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!subscription) {
      console.log("📝 No active subscription found, returning default");
      return {
        planId: "starter",
        status: "inactive",
        isActive: false,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
      };
    }

    console.log(`✅ Found active subscription: ${subscription.planId}`);
    
    return {
      ...subscription,
      isActive: subscription.status === "active",
    };
  },
});

/**
 * Create or update subscription from Clerk webhook
 */
export const upsertSubscription = mutation({
  args: {
    clerkUserId: v.string(),
    clerkSubscriptionId: v.string(),
    planId: v.union(v.literal("starter"), v.literal("pro"), v.literal("enterprise")),
    status: v.union(
      v.literal("active"), 
      v.literal("canceled"), 
      v.literal("past_due"), 
      v.literal("unpaid"),
      v.literal("incomplete")
    ),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    trialEnd: v.optional(v.number()),
    metadata: v.optional(v.object({
      stripeCustomerId: v.optional(v.string()),
      stripeSubscriptionId: v.optional(v.string()),
      lastPaymentDate: v.optional(v.number()),
      nextBillingDate: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Upserting subscription for Clerk user: ${args.clerkUserId}`);
    console.log(`📋 Plan: ${args.planId}, Status: ${args.status}`);

    // Find user by Clerk ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkUserId))
      .first();

    if (!user) {
      console.log("❌ User not found for Clerk ID:", args.clerkUserId);
      throw new Error("User not found");
    }

    console.log(`✅ Found user: ${user.name} (${user._id})`);

    // Check for existing subscription
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_clerk_subscription", (q) => q.eq("clerkSubscriptionId", args.clerkSubscriptionId))
      .first();

    const now = Date.now();
    const subscriptionData = {
      userId: user._id,
      clerkSubscriptionId: args.clerkSubscriptionId,
      planId: args.planId,
      status: args.status,
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAtPeriodEnd: args.cancelAtPeriodEnd || false,
      trialEnd: args.trialEnd,
      metadata: args.metadata,
      updatedAt: now,
    };

    if (existingSubscription) {
      console.log(`🔄 Updating existing subscription: ${existingSubscription._id}`);
      await ctx.db.patch(existingSubscription._id, subscriptionData);
      
      // Update feature access
      await updateFeatureAccess(ctx, user._id, args.planId);
      
      return existingSubscription._id;
    } else {
      console.log("➕ Creating new subscription");
      const subscriptionId = await ctx.db.insert("subscriptions", {
        ...subscriptionData,
        createdAt: now,
      });
      
      // Create feature access
      await updateFeatureAccess(ctx, user._id, args.planId);
      
      console.log(`✅ Created subscription: ${subscriptionId}`);
      return subscriptionId;
    }
  },
});

/**
 * Update feature access based on plan
 */
async function updateFeatureAccess(
  ctx: any, 
  userId: Id<"users">, 
  planId: "starter" | "pro" | "enterprise"
) {
  console.log(`🔐 Updating feature access for plan: ${planId}`);

  const planFeatures = {
    starter: {
      features: {
        basicMonitoring: true,
        premiumAi: false,
        imageGeneration: false,
        bulkProcessing: false,
        advancedAnalytics: false,
        prioritySupport: false,
        customIntegrations: false,
        whiteLabel: false,
      },
      limits: {
        maxAccounts: 3,
        maxAiResponses: 100,
        maxImageGenerations: 0,
        maxApiRequests: 1000,
        maxBulkOperations: 0,
      },
    },
    pro: {
      features: {
        basicMonitoring: true,
        premiumAi: true,
        imageGeneration: true,
        bulkProcessing: false,
        advancedAnalytics: true,
        prioritySupport: true,
        customIntegrations: false,
        whiteLabel: false,
      },
      limits: {
        maxAccounts: 10,
        maxAiResponses: 500,
        maxImageGenerations: 50,
        maxApiRequests: 5000,
        maxBulkOperations: 10,
      },
    },
    enterprise: {
      features: {
        basicMonitoring: true,
        premiumAi: true,
        imageGeneration: true,
        bulkProcessing: true,
        advancedAnalytics: true,
        prioritySupport: true,
        customIntegrations: true,
        whiteLabel: true,
      },
      limits: {
        maxAccounts: -1, // unlimited
        maxAiResponses: -1, // unlimited
        maxImageGenerations: -1, // unlimited
        maxApiRequests: 25000,
        maxBulkOperations: -1, // unlimited
      },
    },
  };

  const planConfig = planFeatures[planId];
  
  // Check for existing feature access
  const existingAccess = await ctx.db
    .query("featureAccess")
    .withIndex("by_user", (q) => q.eq("userId", userId))
    .first();

  const accessData = {
    userId,
    planId,
    features: planConfig.features,
    limits: planConfig.limits,
    updatedAt: Date.now(),
  };

  if (existingAccess) {
    console.log(`🔄 Updating existing feature access`);
    await ctx.db.patch(existingAccess._id, accessData);
  } else {
    console.log(`➕ Creating new feature access`);
    await ctx.db.insert("featureAccess", accessData);
  }

  console.log(`✅ Feature access updated for ${planId} plan`);
}

/**
 * Get user's feature access and limits
 */
export const getUserFeatureAccess = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const featureAccess = await ctx.db
      .query("featureAccess")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .first();

    if (!featureAccess) {
      // Return default starter plan access
      return {
        planId: "starter",
        features: {
          basicMonitoring: true,
          premiumAi: false,
          imageGeneration: false,
          bulkProcessing: false,
          advancedAnalytics: false,
          prioritySupport: false,
          customIntegrations: false,
          whiteLabel: false,
        },
        limits: {
          maxAccounts: 3,
          maxAiResponses: 100,
          maxImageGenerations: 0,
          maxApiRequests: 1000,
          maxBulkOperations: 0,
        },
      };
    }

    return featureAccess;
  },
});

/**
 * Cancel subscription
 */
export const cancelSubscription = mutation({
  args: {
    clerkSubscriptionId: v.string(),
    cancelAtPeriodEnd: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    console.log(`🚫 Canceling subscription: ${args.clerkSubscriptionId}`);

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_clerk_subscription", (q) => q.eq("clerkSubscriptionId", args.clerkSubscriptionId))
      .first();

    if (!subscription) {
      throw new Error("Subscription not found");
    }

    await ctx.db.patch(subscription._id, {
      status: args.cancelAtPeriodEnd ? "active" : "canceled",
      cancelAtPeriodEnd: args.cancelAtPeriodEnd || false,
      updatedAt: Date.now(),
    });

    console.log(`✅ Subscription canceled`);
    return { success: true };
  },
});
