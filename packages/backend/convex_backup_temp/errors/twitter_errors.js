/**
 * Error handling for Twitter API integration
 */
export class TweetIOError extends <PERSON><PERSON>r {
    statusCode;
    rateLimitInfo;
    constructor(message, statusCode, rateLimitInfo) {
        super(message);
        this.statusCode = statusCode;
        this.rateLimitInfo = rateLimitInfo;
        this.name = "TweetIOError";
    }
}
/**
 * Check if an error is a rate limiting error
 */
export function isRateLimitError(error) {
    return error instanceof TweetIOError && error.statusCode === 429;
}
/**
 * Check if an error is a authentication error
 */
export function isAuthError(error) {
    return error instanceof TweetIOError &&
        (error.statusCode === 401 || error.statusCode === 403);
}
/**
 * Check if an error is a not found error
 */
export function isNotFoundError(error) {
    return error instanceof TweetIOError && error.statusCode === 404;
}
/**
 * Check if an error should be retried
 */
export function shouldRetryError(error) {
    if (!(error instanceof TweetIOError)) {
        return true; // Retry network errors
    }
    // Don't retry auth or not found errors
    if (isAuthError(error) || isNotFoundError(error)) {
        return false;
    }
    // Retry rate limit and server errors
    const statusCode = error.statusCode;
    return statusCode === 429 ||
        statusCode === 500 ||
        statusCode === 502 ||
        statusCode === 503 ||
        statusCode === 504;
}
/**
 * Parse error response from TwitterAPI.io
 */
export function parseTwitterApiError(response, errorText) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    try {
        const errorData = JSON.parse(errorText);
        if (errorData.errors && Array.isArray(errorData.errors)) {
            errorMessage = errorData.errors
                .map((e) => e.detail || e.message)
                .join(", ");
        }
    }
    catch {
        // Use the raw error text if JSON parsing fails
        errorMessage = errorText || errorMessage;
    }
    return {
        message: errorMessage,
        code: response.status
    };
}
/**
 * Format rate limit reset time for user display
 */
export function formatRateLimitReset(resetTimestamp) {
    const resetTime = new Date(resetTimestamp * 1000);
    return resetTime.toISOString();
}
/**
 * Calculate time until rate limit reset
 */
export function getTimeUntilReset(resetTimestamp) {
    const now = Math.floor(Date.now() / 1000);
    return Math.max(0, resetTimestamp - now);
}
