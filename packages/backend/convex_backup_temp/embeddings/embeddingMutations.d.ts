/**
 * Generate and store embedding for a tweet
 */
export declare const generateTweetEmbedding: any;
/**
 * Store tweet embedding in database
 */
export declare const storeTweetEmbedding: any;
/**
 * Generate and store embedding for a mention
 */
export declare const generateMentionEmbedding: any;
/**
 * Store mention embedding in database
 */
export declare const storeMentionEmbedding: any;
/**
 * Generate and store embedding for a response
 */
export declare const generateResponseEmbedding: any;
/**
 * Store response embedding in database
 */
export declare const storeResponseEmbedding: any;
/**
 * Generate user context embedding
 */
export declare const generateUserContextEmbedding: any;
/**
 * Store user context embedding
 */
export declare const storeUserContextEmbedding: any;
/**
 * Bulk generate embeddings for existing content
 */
export declare const bulkGenerateEmbeddings: any;
