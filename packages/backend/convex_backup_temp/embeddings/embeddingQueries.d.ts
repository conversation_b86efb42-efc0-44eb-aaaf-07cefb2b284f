/**
 * Find similar tweets using vector search
 */
export declare const findSimilarTweets: any;
/**
 * Find similar mentions using vector search
 */
export declare const findSimilarMentions: any;
/**
 * Find similar successful responses for context
 */
export declare const findSimilarResponses: any;
/**
 * Get user context for personalized responses
 */
export declare const getUserContext: any;
/**
 * Find contextually relevant content for response generation
 */
export declare const findRelevantContext: any;
/**
 * Get embedding statistics
 */
export declare const getEmbeddingStats: any;
/**
 * Get tweet embedding by tweet ID
 */
export declare const getTweetEmbedding: any;
/**
 * Get user context embeddings
 */
export declare const getUserContextEmbeddings: any;
/**
 * Get mention embedding by mention ID
 */
export declare const getMentionEmbedding: any;
/**
 * Get response embedding by response ID
 */
export declare const getResponseEmbedding: any;
