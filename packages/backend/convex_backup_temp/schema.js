import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
export default defineSchema({
    users: defineTable({
        name: v.string(),
        email: v.string(),
        clerkId: v.string(),
        image: v.optional(v.string()),
        primaryWalletId: v.optional(v.id("wallets")),
        walletPreferences: v.optional(v.object({
            preferredBlockchain: v.optional(v.union(v.literal("ethereum"), v.literal("solana"), v.literal("polygon"), v.literal("base"))),
            autoConnectWallet: v.optional(v.boolean()),
            showBalances: v.optional(v.boolean()),
        })),
        lastMentionRefresh: v.optional(v.number()), // Rate limiting for manual refreshes
        createdAt: v.number(),
        updatedAt: v.optional(v.number()),
    }).index("by_clerk_id", ["clerkId"])
        // 🚀 NEW INDEXES FOR USER QUERIES
        .index("by_email", ["email"])
        .index("by_created_at", ["createdAt"])
        .index("by_last_mention_refresh", ["lastMentionRefresh"]),
    wallets: defineTable({
        userId: v.id("users"),
        address: v.string(),
        blockchain: v.union(v.literal("ethereum"), v.literal("solana"), v.literal("polygon"), v.literal("base")),
        walletType: v.string(), // "metamask", "phantom", "coinbase", "walletconnect", etc.
        verified: v.boolean(),
        isPrimary: v.boolean(),
        connectedVia: v.union(v.literal("clerk"), v.literal("manual")), // How wallet was connected
        connectedAt: v.number(),
        lastUsedAt: v.optional(v.number()),
        metadata: v.optional(v.object({
            ensName: v.optional(v.string()), // For Ethereum ENS
            solanaName: v.optional(v.string()), // For Solana Name Service
            balance: v.optional(v.string()), // Store as string to avoid precision issues
            lastBalanceUpdate: v.optional(v.number()),
            publicKey: v.optional(v.string()), // For Solana public key representation
        })),
    }).index("by_user", ["userId"])
        .index("by_address", ["address"])
        .index("by_blockchain", ["blockchain"])
        .index("by_user_primary", ["userId", "isPrimary"])
        .index("by_user_blockchain", ["userId", "blockchain"]),
    credits: defineTable({
        userId: v.id("users"),
        balance: v.number(),
        lastPurchaseAt: v.optional(v.number()),
        lastPurchaseAmount: v.optional(v.number()),
        version: v.number(), // For potential optimistic concurrency or data versioning
    }).index("by_userId", ["userId"]),
    processedTransactions: defineTable({
        signature: v.string(), // Solana transaction signature
        processedAt: v.number(),
        userId: v.id("users"),
        creditsPurchased: v.number(),
        splTokenAmount: v.number(), // Amount of SPL token transferred, in smallest unit
        purchaseTransactionId: v.optional(v.string()), // Optional: if we have our own internal ID for the purchase event
    }).index("by_signature", ["signature"])
        .index("by_userId", ["userId"]),
    walletVerifications: defineTable({
        userId: v.id("users"),
        address: v.string(),
        blockchain: v.union(v.literal("ethereum"), v.literal("solana"), v.literal("polygon"), v.literal("base")),
        challenge: v.string(),
        signature: v.optional(v.string()),
        status: v.union(v.literal("pending"), v.literal("verified"), v.literal("failed"), v.literal("expired")),
        expiresAt: v.number(),
        createdAt: v.number(),
        attemptedAt: v.optional(v.number()),
    }).index("by_user", ["userId"])
        .index("by_address", ["address"])
        .index("by_status", ["status"])
        .index("by_expires", ["expiresAt"]),
    twitterAccounts: defineTable({
        userId: v.id("users"),
        handle: v.string(),
        displayName: v.string(),
        isActive: v.boolean(),
        isMonitoringEnabled: v.optional(v.boolean()),
        lastScrapedAt: v.optional(v.number()), // When this account was last scraped for mentions
        createdAt: v.number(),
        updatedAt: v.optional(v.number()),
    }).index("by_user", ["userId"])
        .index("by_handle", ["handle"])
        // 🚀 NEW COMPOUND INDEXES FOR TWITTER ACCOUNT QUERIES
        .index("by_user_and_active", ["userId", "isActive"])
        .index("by_user_and_monitoring", ["userId", "isMonitoringEnabled"])
        .index("by_active_and_monitoring", ["isActive", "isMonitoringEnabled"])
        .index("by_user_and_created_at", ["userId", "createdAt"])
        .index("by_last_scraped_at", ["lastScrapedAt"]),
    todos: defineTable({
        text: v.string(),
        completed: v.boolean(),
        userId: v.id("users"),
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_user", ["userId"]),
    // Tweets table for storing collected tweets from monitored accounts
    tweets: defineTable({
        twitterAccountId: v.id("twitterAccounts"),
        tweetId: v.string(), // External Twitter ID
        content: v.string(),
        author: v.string(), // Display name
        authorHandle: v.string(), // @username
        authorProfileImage: v.optional(v.string()),
        isRetweet: v.optional(v.boolean()),
        retweetedFrom: v.optional(v.string()),
        createdAt: v.number(), // Tweet creation timestamp
        scrapedAt: v.number(), // When we scraped it
        engagement: v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        }),
        // AI Analysis fields
        analysisStatus: v.union(v.literal("pending"), v.literal("analyzed"), v.literal("response_worthy"), v.literal("skip")),
        analysisScore: v.optional(v.number()), // 0-1 score for response worthiness
        analysisReason: v.optional(v.string()), // Why it's worth responding to
        embeddingId: v.optional(v.string()), // For vector search
        url: v.optional(v.string()), // Full tweet URL
        metadata: v.optional(v.object({
            isThread: v.optional(v.boolean()),
            threadPosition: v.optional(v.number()),
            hasMedia: v.optional(v.boolean()),
            mediaType: v.optional(v.string()),
            language: v.optional(v.string()),
        })),
    }).index("by_account", ["twitterAccountId"])
        .index("by_status", ["analysisStatus"])
        .index("by_tweet_id", ["tweetId"])
        .index("by_author_handle", ["authorHandle"])
        .index("by_engagement", ["engagement.likes"])
        .index("by_scraped_at", ["scrapedAt"])
        .searchIndex("search_content", {
        searchField: "content",
        filterFields: ["twitterAccountId", "analysisStatus", "authorHandle"]
    }),
    // Mentions table for Reply Guy feature - tracking when accounts we monitor are mentioned
    mentions: defineTable({
        // Core mention data
        mentionTweetId: v.string(), // External Twitter ID of the mention
        mentionContent: v.string(), // Content of the mentioning tweet
        mentionAuthor: v.string(), // Display name of person mentioning
        mentionAuthorHandle: v.string(), // @username of person mentioning
        mentionAuthorFollowers: v.optional(v.number()),
        mentionAuthorVerified: v.optional(v.boolean()),
        // What account was mentioned
        monitoredAccountId: v.id("twitterAccounts"), // Which of our accounts was mentioned
        mentionType: v.union(v.literal("mention"), // Direct @mention
        v.literal("reply"), // Reply to our tweet
        v.literal("quote"), // Quote tweet
        v.literal("retweet_with_comment") // Retweet with comment
        ),
        // Context and engagement
        originalTweetId: v.optional(v.string()), // If replying to our tweet
        engagement: v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        }),
        // Priority and processing
        priority: v.union(v.literal("high"), // Verified users, high engagement, industry influencers
        v.literal("medium"), // Good engagement, established accounts
        v.literal("low") // New accounts, low engagement
        ),
        // Processing status
        isProcessed: v.boolean(), // Has AI analyzed this mention?
        aiAnalysisResult: v.optional(v.object({
            shouldRespond: v.boolean(),
            responseStrategy: v.optional(v.string()),
            sentiment: v.optional(v.string()), // positive, negative, neutral
            topics: v.optional(v.array(v.string())),
            confidence: v.optional(v.number()),
        })),
        // Enhanced Sentiment Analysis
        sentimentAnalysis: v.optional(v.object({
            // Core sentiment classification
            sentiment: v.union(v.literal("bullish"), v.literal("bearish"), v.literal("neutral")),
            sentimentScore: v.number(), // 1-100 scale
            confidence: v.number(), // 0-1, AI confidence level
            // Financial market context
            marketSentiment: v.object({
                bullishScore: v.number(), // 0-100
                bearishScore: v.number(), // 0-100
                neutralScore: v.number(), // 0-100
                marketContext: v.array(v.string()), // ["price_action", "fundamentals", "technical_analysis"]
            }),
            // Emotional breakdown for trading psychology
            emotions: v.optional(v.object({
                excitement: v.number(), // 0-100
                fear: v.number(), // 0-100
                greed: v.number(), // 0-100
                fomo: v.number(), // 0-100 (fear of missing out)
                panic: v.number(), // 0-100
            })),
            // Analysis metadata
            reasoning: v.string(), // Why this sentiment was assigned
            keyWords: v.array(v.string()), // Words that influenced the sentiment
            analysisModel: v.string(), // Which AI model was used
            analyzedAt: v.number(), // Timestamp of analysis
        })),
        // Notification tracking
        isNotificationSent: v.boolean(), // Have we notified the user?
        notificationSentAt: v.optional(v.number()),
        // Timestamps
        createdAt: v.number(), // When mention was posted
        discoveredAt: v.number(), // When we found it
        processedAt: v.optional(v.number()), // When AI analyzed it
        // Metadata
        url: v.optional(v.string()), // Full mention URL
        embeddingId: v.optional(v.string()), // For vector search
    }).index("by_monitored_account", ["monitoredAccountId"])
        .index("by_priority", ["priority"])
        .index("by_processed", ["isProcessed"])
        .index("by_notification", ["isNotificationSent"])
        .index("by_discovered_at", ["discoveredAt"])
        .index("by_mention_type", ["mentionType"])
        .index("by_author_handle", ["mentionAuthorHandle"])
        .index("by_tweet_id", ["mentionTweetId"])
        // 🚀 NEW COMPOUND INDEXES FOR OPTIMIZED QUERIES
        .index("by_account_and_discovered_at", ["monitoredAccountId", "discoveredAt"])
        .index("by_account_and_created_at", ["monitoredAccountId", "createdAt"])
        .index("by_account_and_priority", ["monitoredAccountId", "priority"])
        .index("by_account_and_processed", ["monitoredAccountId", "isProcessed"])
        .index("by_account_and_notification", ["monitoredAccountId", "isNotificationSent"])
        .index("by_account_and_type", ["monitoredAccountId", "mentionType"])
        .index("by_priority_and_processed", ["priority", "isProcessed"])
        .index("by_notification_and_discovered", ["isNotificationSent", "discoveredAt"])
        .index("by_processed_and_priority", ["isProcessed", "priority"])
        .searchIndex("search_mentions", {
        searchField: "mentionContent",
        filterFields: ["monitoredAccountId", "priority", "mentionType", "isProcessed"]
    }),
    // Responses table for AI-generated responses to tweets and mentions
    responses: defineTable({
        // What this response is for
        targetType: v.union(v.literal("tweet"), // Response to a tweet
        v.literal("mention") // Response to a mention
        ),
        targetId: v.union(v.id("tweets"), v.id("mentions")), // Reference to tweet or mention
        userId: v.id("users"), // Who this response is for
        // Response content
        content: v.string(), // The actual response text
        style: v.string(), // professional, casual, humorous, technical, supportive, etc.
        characterCount: v.number(),
        // AI generation details
        confidence: v.number(), // 0-1 confidence in this response
        generationModel: v.optional(v.string()), // Which AI model generated it
        contextUsed: v.optional(v.array(v.string())), // What context was used
        // Response strategy and optimization
        responseStrategy: v.optional(v.string()), // engage, educate, promote, support
        estimatedEngagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
        })),
        // Status and approval
        status: v.union(v.literal("draft"), // AI generated, awaiting review
        v.literal("approved"), // User approved for posting
        v.literal("declined"), // User declined
        v.literal("posted"), // Actually posted to Twitter
        v.literal("failed") // Failed to post
        ),
        // Media attachments (optional)
        generatedImage: v.optional(v.string()), // Generated image URL
        imagePrompt: v.optional(v.string()), // Prompt used for image generation
        // Enhancement status
        isEnhanced: v.optional(v.boolean()), // Whether this response was enhanced with context
        // Posting details
        postedAt: v.optional(v.number()),
        postedTweetId: v.optional(v.string()), // External Twitter ID if posted
        actualEngagement: v.optional(v.object({
            likes: v.number(),
            retweets: v.number(),
            replies: v.number(),
            views: v.optional(v.number()),
        })),
        // Timestamps
        createdAt: v.number(),
        updatedAt: v.number(),
        approvedAt: v.optional(v.number()),
        // Quality metrics
        userFeedback: v.optional(v.object({
            rating: v.number(), // 1-5 rating from user
            notes: v.optional(v.string()),
        })),
    }).index("by_user", ["userId"])
        .index("by_target", ["targetType", "targetId"])
        .index("by_status", ["status"])
        .index("by_created_at", ["createdAt"])
        .index("by_style", ["style"])
        .index("by_confidence", ["confidence"])
        // 🚀 NEW COMPOUND INDEXES FOR OPTIMIZED RESPONSE QUERIES
        .index("by_user_and_status", ["userId", "status"])
        .index("by_user_and_created_at", ["userId", "createdAt"])
        .index("by_user_and_style", ["userId", "style"])
        .index("by_status_and_created_at", ["status", "createdAt"])
        .index("by_user_and_target_type", ["userId", "targetType"])
        .index("by_target_and_status", ["targetType", "targetId", "status"])
        .searchIndex("search_responses", {
        searchField: "content",
        filterFields: ["userId", "targetType", "status", "style"]
    }),
    // Response drafts for collaborative editing
    responseDrafts: defineTable({
        responseId: v.id("responses"),
        userId: v.id("users"),
        content: v.string(),
        version: v.number(),
        notes: v.optional(v.string()),
        createdAt: v.number(),
    }).index("by_response", ["responseId"])
        .index("by_user", ["userId"])
        .index("by_version", ["responseId", "version"]),
    // Vector indexes for AI-powered semantic search and context retrieval
    // Tweet content embeddings for finding similar tweets and context
    tweetEmbeddings: defineTable({
        tweetId: v.id("tweets"),
        embedding: v.array(v.number()), // Vector embedding of tweet content
        model: v.string(), // Which embedding model was used
        createdAt: v.number(),
    }).index("by_tweet", ["tweetId"])
        .vectorIndex("by_embedding", {
        vectorField: "embedding",
        dimensions: 1536, // OpenAI text-embedding-3-small dimensions
        filterFields: ["model"]
    }),
    // Mention content embeddings for semantic analysis
    mentionEmbeddings: defineTable({
        mentionId: v.id("mentions"),
        embedding: v.array(v.number()),
        model: v.string(),
        createdAt: v.number(),
    }).index("by_mention", ["mentionId"])
        .vectorIndex("by_embedding", {
        vectorField: "embedding",
        dimensions: 1536,
        filterFields: ["model"]
    }),
    // Response embeddings for finding similar successful responses
    responseEmbeddings: defineTable({
        responseId: v.id("responses"),
        embedding: v.array(v.number()),
        model: v.string(),
        createdAt: v.number(),
    }).index("by_response", ["responseId"])
        .vectorIndex("by_embedding", {
        vectorField: "embedding",
        dimensions: 1536,
        filterFields: ["model"]
    }),
    // User context embeddings for personalized responses
    userContextEmbeddings: defineTable({
        userId: v.id("users"),
        contextType: v.string(), // "interests", "writing_style", "expertise", etc.
        content: v.string(), // The actual context text
        embedding: v.array(v.number()),
        model: v.string(),
        weight: v.optional(v.number()), // Importance weight 0-1
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_user", ["userId"])
        .index("by_context_type", ["userId", "contextType"])
        .vectorIndex("by_embedding", {
        vectorField: "embedding",
        dimensions: 1536,
        filterFields: ["userId", "contextType", "model"]
    }),
    // Workflow status tracking for AI analysis pipelines
    workflowStatus: defineTable({
        workflowId: v.string(),
        userId: v.id("users"),
        totalTweets: v.number(),
        step: v.string(), // "initialized", "generating_embeddings", "analyzing_tweets", "completed", etc.
        progress: v.number(), // 0-100
        options: v.optional(v.object({
            batchSize: v.optional(v.number()),
            enableParallel: v.optional(v.boolean()),
            priority: v.optional(v.string()),
            filters: v.optional(v.array(v.string())),
        })), // Workflow configuration
        metadata: v.optional(v.object({
            currentBatch: v.optional(v.number()),
            totalBatches: v.optional(v.number()),
            lastProcessedId: v.optional(v.string()),
            estimatedTimeRemaining: v.optional(v.number()),
        })), // Step-specific metadata
        summary: v.optional(v.object({
            analyzedTweets: v.optional(v.number()),
            responseWorthy: v.optional(v.number()),
            averageScore: v.optional(v.number()),
            topTopics: v.optional(v.array(v.string())),
        })), // Final workflow summary
        results: v.optional(v.array(v.object({
            id: v.string(),
            type: v.string(),
            score: v.optional(v.number()),
            metadata: v.optional(v.string()),
        }))), // Sample results
        errors: v.optional(v.array(v.object({
            message: v.string(),
            timestamp: v.number(),
            step: v.optional(v.string()),
            severity: v.optional(v.string()),
        }))), // Any errors encountered
        createdAt: v.number(),
        updatedAt: v.number(),
        completedAt: v.optional(v.number()),
    }).index("by_user", ["userId"])
        .index("by_workflow_id", ["workflowId"])
        .index("by_created_at", ["createdAt"]),
    // Generated images storage and metadata
    generatedImages: defineTable({
        // Image storage
        imageUrl: v.string(), // URL to the generated image (OpenAI hosted or base64)
        imageBase64: v.optional(v.string()), // Cached base64 for faster loading
        // Generation metadata
        originalPrompt: v.string(), // The original prompt used for generation
        revisedPrompt: v.optional(v.string()), // OpenAI's revised prompt (DALL-E 3)
        model: v.string(), // dall-e-2, dall-e-3, etc.
        style: v.optional(v.string()), // minimal, vibrant, professional, artistic
        platform: v.optional(v.string()), // twitter, instagram, linkedin
        size: v.optional(v.string()), // 1024x1024, 1792x1024, etc.
        quality: v.optional(v.string()), // standard, hd
        // Associations
        userId: v.optional(v.id("users")), // Who generated this image
        responseId: v.optional(v.id("responses")), // Associated response if any
        tweetId: v.optional(v.id("tweets")), // Associated tweet if any
        // Organization and search
        tags: v.array(v.string()), // User-defined tags for organization
        customName: v.optional(v.string()), // User-defined name
        isPublic: v.boolean(), // Whether this image can be seen by other users
        isFavorite: v.optional(v.boolean()), // User favorited
        // Usage tracking
        downloadCount: v.number(), // How many times downloaded/used
        lastUsedAt: v.optional(v.number()), // When last accessed
        // Timestamps
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_user", ["userId"])
        .index("by_response", ["responseId"])
        .index("by_tweet", ["tweetId"])
        .index("by_platform", ["platform"])
        .index("by_style", ["style"])
        .index("by_model", ["model"])
        .index("by_public", ["isPublic"])
        .index("by_created_at", ["createdAt"])
        .index("by_download_count", ["downloadCount"])
        .searchIndex("search_images", {
        searchField: "originalPrompt",
        filterFields: ["userId", "platform", "style", "model", "isPublic"]
    }),
    // xAI Live Search Results for analytics and history
    searchResults: defineTable({
        searchType: v.string(), // "live_search", "mention_search", "trending_search", "content_analysis", etc.
        query: v.string(), // Original search query
        content: v.string(), // xAI response content
        citations: v.array(v.string()), // Source URLs
        metadata: v.optional(v.object({
            responseTime: v.optional(v.number()),
            success: v.optional(v.boolean()),
            insights: v.optional(v.array(v.string())),
            tokensUsed: v.optional(v.number()),
            model: v.optional(v.string()),
            searchFilters: v.optional(v.array(v.string())),
        })), // Additional search metadata
        userId: v.optional(v.id("users")), // User who performed the search
        createdAt: v.number(),
    }).index("by_user", ["userId"])
        .index("by_type", ["searchType"])
        .index("by_created_at", ["createdAt"])
        .searchIndex("search_queries", {
        searchField: "query",
        filterFields: ["searchType", "userId"]
    }),
    // TwitterAPI.io Usage Tracking for monitoring and quota management
    twitterApiUsage: defineTable({
        timestamp: v.number(), // When the API call was made
        endpoint: v.string(), // Which API endpoint was called
        requestCount: v.number(), // Number of requests made
        responseSize: v.optional(v.number()), // Response size in bytes
        duration: v.optional(v.number()), // Request duration in milliseconds
        status: v.union(v.literal("success"), v.literal("error"), v.literal("rate_limited")), // Request status
        errorMessage: v.optional(v.string()), // Error message if status is error
        cost: v.number(), // Estimated cost in credits/dollars
        createdAt: v.number(), // When this record was created
    }).index("by_timestamp", ["timestamp"])
        .index("by_endpoint", ["endpoint"])
        .index("by_status", ["status"])
        .index("by_created_at", ["createdAt"])
        .index("by_endpoint_timestamp", ["endpoint", "timestamp"])
        // 🚀 NEW COMPOUND INDEXES FOR API USAGE ANALYTICS
        .index("by_endpoint_and_status", ["endpoint", "status"])
        .index("by_status_and_timestamp", ["status", "timestamp"]),
    // Rate limiting and quota tracking
    apiQuotaStatus: defineTable({
        date: v.string(), // YYYY-MM-DD format for daily tracking
        totalRequests: v.number(), // Total requests made on this date
        successfulRequests: v.number(), // Successful requests
        errorRequests: v.number(), // Failed requests
        rateLimitedRequests: v.number(), // Rate limited requests
        totalCost: v.number(), // Total cost for the day
        lastUpdated: v.number(), // When this record was last updated
    }).index("by_date", ["date"])
        .index("by_last_updated", ["lastUpdated"]),
    // Advanced caching system for mention performance optimization
    mentionCache: defineTable({
        key: v.string(), // Cache key (unique identifier)
        data: v.object({
            mentions: v.optional(v.array(v.object({
                id: v.string(),
                content: v.string(),
                author: v.string(),
                timestamp: v.number(),
            }))),
            analytics: v.optional(v.object({
                totalCount: v.number(),
                unreadCount: v.number(),
                highPriorityCount: v.number(),
            })),
            accounts: v.optional(v.array(v.object({
                id: v.string(),
                handle: v.string(),
                isActive: v.boolean(),
            }))),
            searchResults: v.optional(v.array(v.string())),
        }), // Cached data (structured cache data)
        timestamp: v.number(), // When cache entry was created
        ttl: v.number(), // Time to live in milliseconds
        hits: v.number(), // Number of times this cache entry was accessed
        lastAccessed: v.number(), // When cache entry was last accessed
        tags: v.array(v.string()), // Tags for grouped invalidation
    }).index("by_key", ["key"])
        .index("by_timestamp", ["timestamp"])
        .index("by_last_accessed", ["lastAccessed"])
        .index("by_tags", ["tags"])
        // 🚀 NEW COMPOUND INDEXES FOR CACHE PERFORMANCE
        .index("by_timestamp_and_ttl", ["timestamp", "ttl"])
        .index("by_last_accessed_and_hits", ["lastAccessed", "hits"]),
    // Optimization configuration management for auto-tuning and performance settings
    optimizationConfigs: defineTable({
        userId: v.optional(v.id("users")), // User-specific config (null for global)
        settings: v.object({
            // Core optimization settings
            maxMentionsPerBatch: v.number(),
            batchProcessingInterval: v.number(),
            cacheRetentionDays: v.number(),
            enableSemanticCaching: v.boolean(),
            enablePriorityScoring: v.boolean(),
            // Performance thresholds
            maxApiRequestsPerMinute: v.number(),
            adaptiveThrottling: v.boolean(),
            resourceMonitoring: v.boolean(),
            // Quality settings
            minConfidenceThreshold: v.number(),
            enableQualityMetrics: v.boolean(),
            adaptiveQualityControl: v.boolean(),
        }),
        isGlobal: v.optional(v.boolean()), // Whether this is a global configuration
        createdAt: v.number(), // When configuration was created
        updatedAt: v.number(), // When configuration was last updated
    }).index("by_user", ["userId"])
        .index("by_global", ["isGlobal"])
        .index("by_updated_at", ["updatedAt"]),
    // User-specific settings for preferences and response styles
    userSettings: defineTable({
        userId: v.id("users"),
        preferredResponseStyle: v.optional(v.string()), // professional, casual, humorous, etc.
        autoApproveResponses: v.optional(v.boolean()), // Auto-approve AI responses
        notificationPreferences: v.optional(v.object({
            emailEnabled: v.boolean(),
            pushEnabled: v.boolean(),
            priorityOnly: v.boolean(),
        })),
        analysisPreferences: v.optional(v.object({
            enableSemanticAnalysis: v.boolean(),
            minEngagementThreshold: v.number(),
            keywordFilters: v.array(v.string()),
        })),
        apiPreferences: v.optional(v.object({
            preferredModel: v.string(),
            maxTokensPerRequest: v.number(),
            temperatureSetting: v.number(),
        })),
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_user", ["userId"]),
    // Background job tracking and processing
    jobs: defineTable({
        jobType: v.string(), // "mention_monitoring", "tweet_analysis", "response_generation", etc.
        status: v.union(v.literal("pending"), v.literal("running"), v.literal("completed"), v.literal("failed"), v.literal("cancelled")),
        userId: v.optional(v.id("users")), // Associated user if applicable
        progress: v.number(), // 0-100 percentage
        totalItems: v.optional(v.number()), // Total items to process
        processedItems: v.optional(v.number()), // Items processed so far
        metadata: v.optional(v.object({
            description: v.optional(v.string()),
            startedAt: v.optional(v.number()),
            estimatedCompletion: v.optional(v.number()),
            errorCount: v.optional(v.number()),
            lastErrorMessage: v.optional(v.string()),
        })),
        results: v.optional(v.object({
            successCount: v.number(),
            errorCount: v.number(),
            outputData: v.optional(v.array(v.string())), // IDs of created/updated records
        })),
        createdAt: v.number(),
        updatedAt: v.number(),
        completedAt: v.optional(v.number()),
    }).index("by_status", ["status"])
        .index("by_user", ["userId"])
        .index("by_type", ["jobType"])
        .index("by_created_at", ["createdAt"])
        // 🚀 NEW COMPOUND INDEXES FOR JOB PROCESSING
        .index("by_status_and_type", ["status", "jobType"])
        .index("by_user_and_status", ["userId", "status"])
        .index("by_type_and_created_at", ["jobType", "createdAt"])
        .index("by_status_and_created_at", ["status", "createdAt"]),
    // 🚀 ADVANCED CACHING SYSTEM: Multi-layer intelligent cache
    cache: defineTable({
        key: v.string(), // Cache key (unique identifier)
        data: v.any(), // Cached data (any JSON-serializable data)
        expiresAt: v.number(), // Expiration timestamp
        createdAt: v.number(), // Creation timestamp
        tags: v.array(v.string()), // Tags for bulk invalidation
        priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")), // Cache priority
        size: v.number(), // Data size in bytes for monitoring
        metadata: v.optional(v.object({
            hitCount: v.optional(v.number()), // How many times this cache was hit
            lastAccessed: v.optional(v.number()), // Last access timestamp
            computeTime: v.optional(v.number()), // Time taken to compute this data
            source: v.optional(v.string()), // Source function/query that generated this
        })),
    }).index("by_key", ["key"])
        .index("by_expiration", ["expiresAt"])
        .index("by_priority", ["priority"])
        .index("by_created_at", ["createdAt"]),
    // Legacy cache table (will be migrated to new cache table)
    tweetStatsCache: defineTable({
        cacheKey: v.string(), // e.g., "global_tweet_stats" or "tweet_stats_account_<ID>"
        stats: v.any(), // Store the computed stats object
        cachedAt: v.number(),
        expiresAt: v.number(),
    }).index("by_cacheKey", ["cacheKey"])
        .index("by_expiresAt", ["expiresAt"]), // For potential cleanup jobs
    analyticsEvents: defineTable({
        eventName: v.string(),
        timestamp: v.number(),
        userId: v.optional(v.id("users")), // Clerk User ID or your internal User ID
        clerkUserId: v.optional(v.string()), // If using Clerk's ID directly for some events
        sessionId: v.optional(v.string()), // Optional session identifier
        path: v.optional(v.string()), // URL path where event occurred
        properties: v.any(), // Arbitrary JSON object for event-specific data
    }).index("by_timestamp", ["timestamp"])
        .index("by_eventName", ["eventName"])
        .index("by_userId", ["userId"])
        .index("by_clerkUserId", ["clerkUserId"]),
    // 🚀 AI RESPONSE CACHING: Reduce AI costs by 60-80% through intelligent caching
    aiCache: defineTable({
        contentHash: v.string(), // Hash of the content being analyzed
        content: v.string(), // Truncated content for debugging (max 1000 chars)
        responseType: v.union(v.literal("sentiment_analysis"), v.literal("viral_detection"), v.literal("content_analysis"), v.literal("response_generation")),
        aiResponse: v.any(), // The cached AI response
        model: v.string(), // AI model used
        tokensUsed: v.number(), // Tokens consumed by this request
        cost: v.number(), // Estimated cost of this request
        createdAt: v.number(), // When this cache entry was created
        lastUpdated: v.number(), // When this cache entry was last updated
        lastAccessed: v.optional(v.number()), // When this cache entry was last accessed
        ttl: v.number(), // Time to live in milliseconds
        hitCount: v.number(), // How many times this cache entry has been used
    }).index("by_hash", ["contentHash"])
        .index("by_type", ["responseType"])
        .index("by_created", ["createdAt"])
        .index("by_model", ["model"])
        // 🚀 NEW COMPOUND INDEXES FOR AI CACHE OPTIMIZATION
        .index("by_type_and_model", ["responseType", "model"])
        .index("by_model_and_created", ["model", "createdAt"])
        .index("by_type_and_created", ["responseType", "createdAt"])
        .index("by_last_accessed_and_hit_count", ["lastAccessed", "hitCount"]),
    // 🚀 SCHEMA NORMALIZATION: Separate sentiment analysis data to reduce storage by 70-80%
    sentimentAnalysis: defineTable({
        mentionId: v.id("mentions"), // Reference to the mention
        sentiment: v.union(v.literal("bullish"), v.literal("bearish"), v.literal("neutral")),
        sentimentScore: v.number(), // 1-100 scale
        confidence: v.number(), // 0-1, AI confidence level
        reasoning: v.string(), // Why this sentiment was assigned
        keyWords: v.array(v.string()), // Words that influenced the sentiment (max 5)
        analysisModel: v.string(), // Which AI model was used
        analyzedAt: v.number(), // Timestamp of analysis
    }).index("by_mention", ["mentionId"])
        .index("by_sentiment", ["sentiment"])
        .index("by_score", ["sentimentScore"])
        .index("by_analyzed_at", ["analyzedAt"]),
    // Market sentiment breakdown (normalized from mentions.sentimentAnalysis.marketSentiment)
    marketSentiment: defineTable({
        mentionId: v.id("mentions"), // Reference to the mention
        bullishScore: v.number(), // 0-100
        bearishScore: v.number(), // 0-100
        neutralScore: v.number(), // 0-100
        marketContext: v.array(v.string()), // ["price_action", "fundamentals", "technical_analysis"]
        analyzedAt: v.number(),
    }).index("by_mention", ["mentionId"])
        .index("by_bullish_score", ["bullishScore"])
        .index("by_bearish_score", ["bearishScore"]),
    // Emotional analysis breakdown (normalized from mentions.sentimentAnalysis.emotions)
    emotionalAnalysis: defineTable({
        mentionId: v.id("mentions"), // Reference to the mention
        emotionType: v.union(v.literal("excitement"), v.literal("fear"), v.literal("greed"), v.literal("fomo"), v.literal("panic")),
        score: v.number(), // 0-100
        analyzedAt: v.number(),
    }).index("by_mention", ["mentionId"])
        .index("by_emotion_type", ["emotionType"])
        .index("by_score", ["score"]),
    // 🚀 BANDWIDTH MONITORING: Track optimization effectiveness
    bandwidthLogs: defineTable({
        operation: v.string(), // Query/function name
        bytesRead: v.number(), // Actual bytes transferred
        executionTime: v.number(), // Query execution time in ms
        recordsScanned: v.number(), // Number of records examined
        recordsReturned: v.number(), // Number of records returned
        cacheHit: v.boolean(), // Whether this was served from cache
        optimizationType: v.optional(v.string()), // "projection", "pagination", "caching"
        estimatedSavings: v.optional(v.number()), // Estimated bytes saved by optimization
        timestamp: v.number(), // When the operation occurred
    }).index("by_timestamp", ["timestamp"])
        .index("by_operation", ["operation"])
        .index("by_optimization_type", ["optimizationType"])
        // 🚀 NEW COMPOUND INDEXES FOR BANDWIDTH ANALYTICS
        .index("by_operation_and_timestamp", ["operation", "timestamp"])
        .index("by_cache_hit_and_timestamp", ["cacheHit", "timestamp"])
        .index("by_operation_and_cache_hit", ["operation", "cacheHit"]),
    // 💳 SUBSCRIPTION MANAGEMENT: Clerk billing integration
    subscriptions: defineTable({
        userId: v.id("users"),
        clerkSubscriptionId: v.string(), // Clerk's subscription ID
        planId: v.union(v.literal("starter"), v.literal("pro"), v.literal("enterprise")),
        status: v.union(v.literal("active"), v.literal("canceled"), v.literal("past_due"), v.literal("unpaid"), v.literal("incomplete")),
        currentPeriodStart: v.number(),
        currentPeriodEnd: v.number(),
        cancelAtPeriodEnd: v.boolean(),
        trialEnd: v.optional(v.number()),
        metadata: v.optional(v.object({
            stripeCustomerId: v.optional(v.string()),
            stripeSubscriptionId: v.optional(v.string()),
            lastPaymentDate: v.optional(v.number()),
            nextBillingDate: v.optional(v.number()),
        })),
        createdAt: v.number(),
        updatedAt: v.number(),
    }).index("by_user", ["userId"])
        .index("by_clerk_subscription", ["clerkSubscriptionId"])
        .index("by_plan", ["planId"])
        .index("by_status", ["status"])
        .index("by_period_end", ["currentPeriodEnd"])
        // 🚀 NEW COMPOUND INDEXES FOR SUBSCRIPTION QUERIES
        .index("by_user_and_status", ["userId", "status"])
        .index("by_plan_and_status", ["planId", "status"])
        .index("by_status_and_period_end", ["status", "currentPeriodEnd"]),
    // 📊 USAGE TRACKING: Monitor feature usage for billing limits
    usageTracking: defineTable({
        userId: v.id("users"),
        date: v.string(), // YYYY-MM-DD format for daily tracking
        planId: v.union(v.literal("starter"), v.literal("pro"), v.literal("enterprise")),
        usage: v.object({
            aiResponses: v.number(),
            imageGenerations: v.number(),
            apiRequests: v.number(),
            bulkOperations: v.number(),
            premiumAiCalls: v.number(),
            analyticsQueries: v.number(),
        }),
        limits: v.object({
            aiResponses: v.number(),
            imageGenerations: v.number(),
            apiRequests: v.number(),
            bulkOperations: v.number(),
            premiumAiCalls: v.number(),
            analyticsQueries: v.number(),
        }),
        lastUpdated: v.number(),
    }).index("by_user_date", ["userId", "date"])
        .index("by_date", ["date"])
        .index("by_plan", ["planId"])
        .index("by_last_updated", ["lastUpdated"])
        // 🚀 NEW COMPOUND INDEXES FOR USAGE ANALYTICS
        .index("by_plan_and_date", ["planId", "date"])
        .index("by_user_and_last_updated", ["userId", "lastUpdated"]),
    // 🔐 FEATURE ACCESS: Subscription-based feature control
    featureAccess: defineTable({
        userId: v.id("users"),
        planId: v.union(v.literal("starter"), v.literal("pro"), v.literal("enterprise")),
        features: v.object({
            basicMonitoring: v.boolean(),
            premiumAi: v.boolean(),
            imageGeneration: v.boolean(),
            bulkProcessing: v.boolean(),
            advancedAnalytics: v.boolean(),
            prioritySupport: v.boolean(),
            customIntegrations: v.boolean(),
            whiteLabel: v.boolean(),
        }),
        limits: v.object({
            maxAccounts: v.number(),
            maxAiResponses: v.number(),
            maxImageGenerations: v.number(),
            maxApiRequests: v.number(),
            maxBulkOperations: v.number(),
        }),
        updatedAt: v.number(),
    }).index("by_user", ["userId"])
        .index("by_plan", ["planId"]),
});
