/**
 * Image storage and management for generated images
 */
/**
 * Store image metadata in the database
 */
export declare const storeImageMetadata: any;
/**
 * Internal mutation to update the imageBase64 field.
 * This is called by the downloadImageAsBase64 action.
 */
export declare const internalUpdateImageBase64: any;
/**
 * Get image metadata by ID
 */
export declare const getImageMetadata: any;
/**
 * Get images by user
 */
export declare const getUserImages: any;
/**
 * Get images by response ID
 */
export declare const getResponseImages: any;
/**
 * Update image metadata
 */
export declare const updateImageMetadata: any;
/**
 * Delete image metadata
 */
export declare const deleteImageMetadata: any;
/**
 * Track image download/usage
 */
export declare const trackImageUsage: any;
/**
 * Search images by tags or content
 */
export declare const searchImages: any;
/**
 * Get popular/trending images
 */
export declare const getPopularImages: any;
/**
 * Download image as base64 (for caching/optimization)
 */
export declare const downloadImageAsBase64: any;
/**
 * Cleanup old images (for maintenance)
 */
export declare const cleanupOldImages: any;
/**
 * Helper query for cleanup
 */
export declare const getAllImages: any;
