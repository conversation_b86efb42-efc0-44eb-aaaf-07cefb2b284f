import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// TODO: Replace with actual addresses before deployment
const SPL_TOKEN_MINT_ADDRESS = "TOKEN_MINT_ADDRESS_PLACEHOLDER";
const PROJECT_WALLET_ADDRESS = "PROJECT_WALLET_ADDRESS_PLACEHOLDER";

/**
 * Get the credit balance for the currently authenticated user.
 */
export const getCredits = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const creditRecord = await ctx.db
      .query("credits")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (!creditRecord) {
      // TODO: Consider creating a new record with 0 balance here
      // For now, just returning 0 if no record exists.
      return { balance: 0 };
    }

    return { balance: creditRecord.balance };
  },
});

/**
 * Purchase credits with SPL tokens.
 * Verifies a Solana transaction and updates credit balance.
 */
export const purchaseCredits = mutation({
  args: {
    transactionSignature: v.string(),
    // Amount of SPL token expected to be transferred, in the token's smallest unit.
    // For example, if your token has 9 decimal places, and user sends 1 token, this would be 1_000_000_000.
    splTokenAmount: v.number(),
    creditsToPurchase: v.number(), // The number of credits the user expects to receive
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // 1. Check if this transaction signature has already been processed
    const existingTransaction = await ctx.db
      .query("processedTransactions")
      .withIndex("by_signature", (q) => q.eq("signature", args.transactionSignature))
      .first();

    if (existingTransaction) {
      throw new Error("Transaction already processed.");
    }

    // 2. TODO: Implement Solana Transaction Verification
    //    - Import '@solana/web3.js' (Connection, PublicKey, etc.)
    //    - Create a Connection to Solana mainnet-beta.
    //    - Fetch the transaction details using args.transactionSignature.
    //    - Verify:
    //        - The transaction was successful.
    //        - It's recent (e.g., within the last hour or configurable window).
    //        - It involves a transfer of `args.splTokenAmount` of `SPL_TOKEN_MINT_ADDRESS`
    //          from one of the user's verified wallets (or any wallet, depending on security model for v1)
    //          to `PROJECT_WALLET_ADDRESS`.
    //        - The recipient (PROJECT_WALLET_ADDRESS) actually received the tokens.
    //    - If verification fails, throw an error.
    const isTransactionVerified = false; // Placeholder
    // const transactionDetails = await verifySolanaTransaction(args.transactionSignature, args.splTokenAmount);
    // if (!transactionDetails.verified) {
    //   throw new Error(transactionDetails.error || "Solana transaction verification failed.");
    // }

    if (!isTransactionVerified) { // Temporary check until verification is implemented
        // This is a placeholder. In a real scenario, we'd rely on the verification function.
        // For now, to allow UI to be built, we might bypass this if a special flag is set,
        // OR simply throw error until it's built. For safety, we'll throw.
        console.warn("Solana transaction verification is not yet implemented. Skipping credit purchase.");
        throw new Error("Solana transaction verification is not yet implemented.");
    }


    // 3. Update credit balance
    const currentCreditRecord = await ctx.db
      .query("credits")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    const newBalance = (currentCreditRecord ? currentCreditRecord.balance : 0) + args.creditsToPurchase;
    const newVersion = (currentCreditRecord ? currentCreditRecord.version : 0) + 1;

    if (currentCreditRecord) {
      await ctx.db.patch(currentCreditRecord._id, {
        balance: newBalance,
        lastPurchaseAt: Date.now(),
        lastPurchaseAmount: args.creditsToPurchase,
        version: newVersion,
      });
    } else {
      await ctx.db.insert("credits", {
        userId: user._id,
        balance: newBalance,
        lastPurchaseAt: Date.now(),
        lastPurchaseAmount: args.creditsToPurchase,
        version: 1,
      });
    }

    // 4. Record the processed transaction
    await ctx.db.insert("processedTransactions", {
      signature: args.transactionSignature,
      processedAt: Date.now(),
      userId: user._id,
      creditsPurchased: args.creditsToPurchase,
      splTokenAmount: args.splTokenAmount,
      // purchaseTransactionId: generated_uuid_if_any,
    });

    return { success: true, newBalance };
  },
});

// Helper function placeholder for Solana transaction verification
// async function verifySolanaTransaction(signature: string, expectedAmount: number) {
//   // TODO: Implement this function using @solana/web3.js
//   // This function will interact with the Solana blockchain.
//   // Ensure @solana/web3.js is added as a dependency.
//   console.log(signature, expectedAmount); // Keep linters happy
//   return { verified: false, error: "Not implemented" };
// }
