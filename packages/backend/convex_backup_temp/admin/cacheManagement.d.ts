/**
 * 🚀 CACHE MANAGEMENT ADMIN TOOLS
 *
 * Administrative functions for monitoring and managing the caching system
 */
/**
 * Get comprehensive cache statistics for admin dashboard
 */
export declare const getCacheAnalytics: import("convex/server").RegisteredQuery<"public", {
    hours?: number | undefined;
}, Promise<{
    overview: {
        totalEntries: number;
        totalSize: number;
        sizeByPriority: Record<string, number>;
        entriesByTag: Record<string, number>;
        averageAge: number;
        expiredCount: number;
    };
    trends: Record<string, any>;
    topKeys: {
        key: string;
        size: number;
        sizeMB: number;
        priority: "high" | "medium" | "low";
        tags: string[];
        age: number;
        expired: boolean;
    }[];
    tagEfficiency: Record<string, any>;
    timeRange: string;
    generatedAt: number;
}>>;
/**
 * Get cache hit/miss statistics from bandwidth logs
 */
export declare const getCachePerformanceMetrics: import("convex/server").RegisteredQuery<"public", {
    hours?: number | undefined;
}, Promise<{
    totalRequests: number;
    cacheHitRate: number;
    totalSavings: number;
    averageResponseTime: number;
    operationBreakdown: {};
    cacheHits?: undefined;
    totalSavingsMB?: undefined;
    timeRange?: undefined;
    generatedAt?: undefined;
} | {
    totalRequests: number;
    cacheHits: number;
    cacheHitRate: number;
    totalSavings: number;
    totalSavingsMB: number;
    averageResponseTime: number;
    operationBreakdown: Record<string, any>;
    timeRange: string;
    generatedAt: number;
}>>;
/**
 * Clear cache by tags (admin function)
 */
export declare const clearCacheByTags: import("convex/server").RegisteredMutation<"public", {
    reason?: string | undefined;
    tags: string[];
}, Promise<{
    success: boolean;
    deletedEntries: number;
    tags: string[];
    reason: string | undefined;
    clearedAt: number;
}>>;
/**
 * Clear expired cache entries (maintenance function)
 */
export declare const clearExpiredCache: import("convex/server").RegisteredMutation<"public", {}, Promise<{
    success: boolean;
    deletedEntries: number;
    cleanedAt: number;
}>>;
/**
 * Get cache entry details by key (debugging tool)
 */
export declare const getCacheEntry: import("convex/server").RegisteredQuery<"public", {
    key: string;
}, Promise<{
    found: boolean;
    key: string;
    data?: undefined;
    age?: undefined;
    isStale?: undefined;
    sizeEstimate?: undefined;
    retrievedAt?: undefined;
} | {
    found: boolean;
    key: string;
    data: unknown;
    age: number;
    isStale: boolean;
    sizeEstimate: number;
    retrievedAt: number;
}>>;
/**
 * Warm cache for specific user (admin tool)
 */
export declare const warmUserCache: import("convex/server").RegisteredMutation<"public", {
    operations?: string[] | undefined;
    userId: import("convex/values").GenericId<"users">;
}, Promise<{
    success: boolean;
    userId: import("convex/values").GenericId<"users">;
    operations: string[];
    warmedAt: number;
    message: string;
}>>;
/**
 * Get cache size and performance recommendations
 */
export declare const getCacheRecommendations: import("convex/server").RegisteredQuery<"public", {}, Promise<{
    stats: {
        totalEntries: number;
        totalSizeMB: number;
        expiredCount: number;
        averageAgeHours: number;
    };
    recommendations: {
        type: string;
        category: string;
        message: string;
        action: string;
    }[];
    generatedAt: number;
}>>;
/**
 * Internal function to automatically cleanup expired cache (called by cron)
 */
export declare const autoCleanupExpiredCache: import("convex/server").RegisteredMutation<"internal", {}, Promise<number>>;
