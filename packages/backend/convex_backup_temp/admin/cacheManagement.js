/**
 * 🚀 CACHE MANAGEMENT ADMIN TOOLS
 *
 * Administrative functions for monitoring and managing the caching system
 */
import { query, mutation, internalMutation } from "../_generated/server";
import { v } from "convex/values";
import { AdvancedCacheHelper } from "../lib/advancedCaching";
/**
 * Get comprehensive cache statistics for admin dashboard
 */
export const getCacheAnalytics = query({
    args: {
        hours: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const stats = await cache.getCacheStats();
        const hoursAgo = Date.now() - (args.hours || 24) * 60 * 60 * 1000;
        // Get cache entries created in the time window
        const recentEntries = await ctx.db
            .query("cache")
            .withIndex("by_created_at", q => q.gte("createdAt", hoursAgo))
            .take(10000);
        // Calculate hit/miss ratios and trends
        const now = Date.now();
        const timeWindows = {
            "1h": now - 60 * 60 * 1000,
            "6h": now - 6 * 60 * 60 * 1000,
            "24h": now - 24 * 60 * 60 * 1000,
        };
        const trends = {};
        for (const [window, timestamp] of Object.entries(timeWindows)) {
            const windowEntries = recentEntries.filter(entry => entry.createdAt >= timestamp);
            const windowSize = windowEntries.reduce((sum, entry) => sum + entry.size, 0);
            trends[window] = {
                entries: windowEntries.length,
                totalSize: windowSize,
                totalSizeMB: Math.round(windowSize / 1024 / 1024 * 100) / 100,
                averageSize: windowEntries.length > 0 ? Math.round(windowSize / windowEntries.length) : 0,
            };
        }
        // Top cache keys by size
        const topKeys = recentEntries
            .sort((a, b) => b.size - a.size)
            .slice(0, 20)
            .map(entry => ({
            key: entry.key,
            size: entry.size,
            sizeMB: Math.round(entry.size / 1024 / 1024 * 100) / 100,
            priority: entry.priority,
            tags: entry.tags,
            age: now - entry.createdAt,
            expired: entry.expiresAt < now,
        }));
        // Cache efficiency by tag
        const tagEfficiency = {};
        for (const entry of recentEntries) {
            for (const tag of entry.tags) {
                if (!tagEfficiency[tag]) {
                    tagEfficiency[tag] = {
                        entries: 0,
                        totalSize: 0,
                        avgSize: 0,
                        hitCount: 0,
                    };
                }
                tagEfficiency[tag].entries += 1;
                tagEfficiency[tag].totalSize += entry.size;
                tagEfficiency[tag].hitCount += entry.metadata?.hitCount || 0;
            }
        }
        // Calculate averages
        Object.keys(tagEfficiency).forEach(tag => {
            const data = tagEfficiency[tag];
            data.avgSize = data.entries > 0 ? Math.round(data.totalSize / data.entries) : 0;
            data.avgHits = data.entries > 0 ? Math.round(data.hitCount / data.entries) : 0;
        });
        return {
            overview: stats,
            trends,
            topKeys,
            tagEfficiency,
            timeRange: `${args.hours || 24} hours`,
            generatedAt: Date.now(),
        };
    },
});
/**
 * Get cache hit/miss statistics from bandwidth logs
 */
export const getCachePerformanceMetrics = query({
    args: {
        hours: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const hoursAgo = Date.now() - (args.hours || 24) * 60 * 60 * 1000;
        const logs = await ctx.db
            .query("bandwidthLogs")
            .withIndex("by_timestamp", q => q.gte("timestamp", hoursAgo))
            .filter(q => q.eq(q.field("optimizationType"), "advanced_caching"))
            .take(50000);
        if (logs.length === 0) {
            return {
                totalRequests: 0,
                cacheHitRate: 0,
                totalSavings: 0,
                averageResponseTime: 0,
                operationBreakdown: {},
            };
        }
        const cacheHits = logs.filter(log => log.cacheHit).length;
        const cacheHitRate = (cacheHits / logs.length) * 100;
        const totalSavings = logs.reduce((sum, log) => sum + (log.estimatedSavings || 0), 0);
        const averageResponseTime = logs.reduce((sum, log) => sum + log.executionTime, 0) / logs.length;
        // Breakdown by operation
        const operationBreakdown = logs.reduce((acc, log) => {
            if (!acc[log.operation]) {
                acc[log.operation] = {
                    totalRequests: 0,
                    cacheHits: 0,
                    cacheHitRate: 0,
                    totalSavings: 0,
                    avgResponseTime: 0,
                    totalResponseTime: 0,
                };
            }
            acc[log.operation].totalRequests += 1;
            if (log.cacheHit)
                acc[log.operation].cacheHits += 1;
            acc[log.operation].totalSavings += log.estimatedSavings || 0;
            acc[log.operation].totalResponseTime += log.executionTime;
            return acc;
        }, {});
        // Calculate rates and averages
        Object.keys(operationBreakdown).forEach(op => {
            const data = operationBreakdown[op];
            data.cacheHitRate = Math.round((data.cacheHits / data.totalRequests) * 100);
            data.avgResponseTime = Math.round(data.totalResponseTime / data.totalRequests);
        });
        return {
            totalRequests: logs.length,
            cacheHits,
            cacheHitRate: Math.round(cacheHitRate * 100) / 100,
            totalSavings: Math.round(totalSavings),
            totalSavingsMB: Math.round(totalSavings / 1024 / 1024 * 100) / 100,
            averageResponseTime: Math.round(averageResponseTime),
            operationBreakdown,
            timeRange: `${args.hours || 24} hours`,
            generatedAt: Date.now(),
        };
    },
});
/**
 * Clear cache by tags (admin function)
 */
export const clearCacheByTags = mutation({
    args: {
        tags: v.array(v.string()),
        reason: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const deletedCount = await cache.invalidateByTags(args.tags);
        // Log the cache clearing action
        console.log(`Cache cleared by admin: ${deletedCount} entries removed for tags: ${args.tags.join(", ")}. Reason: ${args.reason || "Not specified"}`);
        return {
            success: true,
            deletedEntries: deletedCount,
            tags: args.tags,
            reason: args.reason,
            clearedAt: Date.now(),
        };
    },
});
/**
 * Clear expired cache entries (maintenance function)
 */
export const clearExpiredCache = mutation({
    args: {},
    handler: async (ctx) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const deletedCount = await cache.cleanupExpired();
        console.log(`Cache maintenance: ${deletedCount} expired entries removed`);
        return {
            success: true,
            deletedEntries: deletedCount,
            cleanedAt: Date.now(),
        };
    },
});
/**
 * Get cache entry details by key (debugging tool)
 */
export const getCacheEntry = query({
    args: {
        key: v.string(),
    },
    handler: async (ctx, args) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const result = await cache.get(args.key, { allowStale: true });
        if (!result.cacheHit) {
            return {
                found: false,
                key: args.key,
            };
        }
        return {
            found: true,
            key: args.key,
            data: result.data,
            age: result.age,
            isStale: result.isStale,
            sizeEstimate: JSON.stringify(result.data).length,
            retrievedAt: Date.now(),
        };
    },
});
/**
 * Warm cache for specific user (admin tool)
 */
export const warmUserCache = mutation({
    args: {
        userId: v.id("users"),
        operations: v.optional(v.array(v.string())),
    },
    handler: async (ctx, args) => {
        // This would trigger cache warming for specific operations
        // For now, we'll just log the request
        const operations = args.operations || [
            "dashboard_stats",
            "mention_stats",
            "user_mentions",
            "tweet_stats"
        ];
        console.log(`Cache warming requested for user ${args.userId} with operations: ${operations.join(", ")}`);
        // In a real implementation, this would:
        // 1. Get user's accounts
        // 2. Pre-compute and cache the specified operations
        // 3. Return warming results
        return {
            success: true,
            userId: args.userId,
            operations,
            warmedAt: Date.now(),
            message: "Cache warming initiated (implementation pending)",
        };
    },
});
/**
 * Get cache size and performance recommendations
 */
export const getCacheRecommendations = query({
    args: {},
    handler: async (ctx) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const stats = await cache.getCacheStats();
        const recommendations = [];
        // Check total cache size
        const totalSizeMB = stats.totalSize / 1024 / 1024;
        if (totalSizeMB > 1000) { // Over 1GB
            recommendations.push({
                type: "warning",
                category: "storage",
                message: `Cache size is ${Math.round(totalSizeMB)}MB. Consider reducing TTLs or clearing old entries.`,
                action: "Reduce cache TTLs for low-priority items",
            });
        }
        // Check expired entries
        if (stats.expiredCount > stats.totalEntries * 0.2) { // Over 20% expired
            recommendations.push({
                type: "maintenance",
                category: "cleanup",
                message: `${stats.expiredCount} expired entries (${Math.round(stats.expiredCount / stats.totalEntries * 100)}%) found.`,
                action: "Run expired cache cleanup",
            });
        }
        // Check average age
        const averageAgeHours = stats.averageAge / (60 * 60 * 1000);
        if (averageAgeHours > 24) {
            recommendations.push({
                type: "optimization",
                category: "ttl",
                message: `Average cache age is ${Math.round(averageAgeHours)} hours. Consider shorter TTLs for better freshness.`,
                action: "Review and adjust cache TTL settings",
            });
        }
        // Check priority distribution
        const highPrioritySize = stats.sizeByPriority.high || 0;
        const totalSize = stats.totalSize;
        if (highPrioritySize < totalSize * 0.3) {
            recommendations.push({
                type: "optimization",
                category: "priority",
                message: `Only ${Math.round(highPrioritySize / totalSize * 100)}% of cache is high priority. Consider prioritizing critical operations.`,
                action: "Review cache priority assignments",
            });
        }
        return {
            stats: {
                totalEntries: stats.totalEntries,
                totalSizeMB: Math.round(totalSizeMB * 100) / 100,
                expiredCount: stats.expiredCount,
                averageAgeHours: Math.round(averageAgeHours * 100) / 100,
            },
            recommendations,
            generatedAt: Date.now(),
        };
    },
});
/**
 * Internal function to automatically cleanup expired cache (called by cron)
 */
export const autoCleanupExpiredCache = internalMutation({
    args: {},
    handler: async (ctx) => {
        const cache = new AdvancedCacheHelper(ctx.db);
        const deletedCount = await cache.cleanupExpired();
        console.log(`Automatic cache cleanup: ${deletedCount} expired entries removed`);
        return deletedCount;
    },
});
