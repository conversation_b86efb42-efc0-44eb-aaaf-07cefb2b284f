import { mutation, action, query } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getOpenRouterClient } from "../lib/openrouter_client";
import { checkRateLimit } from "../lib/rate_limiter";

/**
 * Viral Detection and Trend Analysis Engine
 * Identifies viral mentions and trending conversations for priority processing
 */

export interface ViralAnalysisInput {
  content: string;
  authorHandle: string;
  authorFollowerCount?: number;
  authorIsVerified?: boolean;
  currentEngagement?: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  timePosted?: number;
  hasMedia?: boolean;
  hashtags?: string[];
  mentions?: string[];
  context?: {
    trendingTopics?: string[];
    currentEvents?: string[];
    authorRecentPerformance?: number[];
  };
}

export interface ViralPrediction {
  viralProbability: number; // 0-1 probability of going viral
  predictedEngagement: {
    likes: number;
    retweets: number;
    replies: number;
    peakTime: number; // Hours from now when peak engagement expected
  };
  viralFactors: {
    contentQuality: number;
    authorInfluence: number;
    timingBonus: number;
    trendAlignment: number;
    emotionalImpact: number;
    shareability: number;
  };
  actionRecommendations: string[];
  confidenceLevel: number;
  riskFactors: string[];
}

/**
 * Main Viral Detection Action
 */
export const predictViralPotential = action({
  args: {
    input: v.object({
      content: v.string(),
      authorHandle: v.string(),
      authorFollowerCount: v.optional(v.number()),
      authorIsVerified: v.optional(v.boolean()),
      currentEngagement: v.optional(v.object({
        likes: v.number(),
        retweets: v.number(),
        replies: v.number(),
        views: v.optional(v.number()),
      })),
      timePosted: v.optional(v.number()),
      hasMedia: v.optional(v.boolean()),
      hashtags: v.optional(v.array(v.string())),
      mentions: v.optional(v.array(v.string())),
      context: v.optional(v.object({
        trendingTopics: v.optional(v.array(v.string())),
        currentEvents: v.optional(v.array(v.string())),
        authorRecentPerformance: v.optional(v.array(v.number())),
      })),
    }),
  },
  handler: async (ctx, { input }): Promise<ViralPrediction> => {
    try {
      // 🔐 SECURITY: Apply rate limiting for expensive AI operations
      await checkRateLimit(ctx, 'predictViralPotential', 'expensive');

      // Step 1: Analyze content patterns and quality
      const contentAnalysis = await analyzeContentForVirality(input);
      
      // Step 2: Calculate author influence score
      const authorInfluence = calculateAuthorInfluence(input);
      
      // Step 3: Assess timing and context factors
      const timingAnalysis = analyzeTimingFactors(input);
      
      // Step 4: Check trend alignment
      const trendAlignment = await analyzeTrendAlignment(input);
      
      // Step 5: Calculate emotional impact score
      const emotionalImpact = await analyzeEmotionalImpact(input);
      
      // Step 6: Assess shareability metrics
      const shareability = calculateShareability(input);
      
      // Step 7: Build comprehensive viral prediction
      const prediction = buildViralPrediction({
        contentQuality: contentAnalysis.qualityScore,
        authorInfluence,
        timingBonus: timingAnalysis.timingScore,
        trendAlignment: trendAlignment.alignmentScore,
        emotionalImpact: emotionalImpact.impactScore,
        shareability,
      }, input);
      
      return prediction;
      
    } catch (error) {
      console.error('Viral prediction failed:', error);
      
      // Fallback prediction based on basic metrics
      return generateFallbackPrediction(input);
    }
  },
});

/**
 * Advanced Content Analysis for Virality
 */
async function analyzeContentForVirality(input: ViralAnalysisInput) {
  const client = getOpenRouterClient();
  
  const viralAnalysisPrompt = `
Analyze this tweet content for viral potential using advanced social media expertise:

Content: "${input.content}"
Author: @${input.authorHandle} ${input.authorIsVerified ? '(Verified)' : ''}
Followers: ${input.authorFollowerCount || 'Unknown'}
${input.currentEngagement ? `Current engagement: ${input.currentEngagement.likes} likes, ${input.currentEngagement.retweets} retweets` : ''}
${input.hashtags?.length ? `Hashtags: ${input.hashtags.join(', ')}` : ''}
${input.hasMedia ? 'Contains media: Yes' : ''}

Analyze the following viral potential factors and provide scores (0-100):

1. Content Quality & Structure
   - Clarity and readability
   - Hook effectiveness (first 10 words)
   - Emotional resonance
   - Uniqueness and originality

2. Engagement Triggers
   - Call-to-action presence
   - Question or poll elements
   - Controversial or debate-worthy aspects
   - Shareability factors

3. Content Type Classification
   - News/breaking information
   - Humor/entertainment value
   - Educational/informative
   - Personal story/relatable
   - Inspirational/motivational

4. Viral Pattern Matching
   - Follows known viral formats
   - Optimal length for sharing
   - Uses viral language patterns
   - Has meme potential

Provide analysis in this exact JSON format:
{
  "qualityScore": number (0-100),
  "engagementTriggers": {
    "hasCallToAction": boolean,
    "hasQuestion": boolean,
    "isControversial": boolean,
    "isShareable": boolean,
    "score": number (0-100)
  },
  "contentType": {
    "primary": "news" | "humor" | "educational" | "personal" | "inspirational",
    "viralPotential": number (0-100)
  },
  "viralPatterns": {
    "followsViralFormat": boolean,
    "optimalLength": boolean,
    "usesTrendingLanguage": boolean,
    "memePotential": boolean,
    "score": number (0-100)
  },
  "hookEffectiveness": number (0-100),
  "overallViralScore": number (0-100),
  "riskFactors": [string],
  "strengthFactors": [string]
}`;

  try {
    const response = await client.generateCompletion(viralAnalysisPrompt, {
      model: 'google/gemini-2.0-flash-exp:free',
      systemPrompt: 'You are a viral content expert with deep knowledge of social media engagement patterns and viral mechanics.',
      maxTokens: 800,
      temperature: 0.3,
    });
    
    const analysis = JSON.parse(response.content);
    return analysis;
  } catch (error) {
    console.error('Content analysis failed:', error);
    return {
      qualityScore: 50,
      engagementTriggers: { score: 40 },
      contentType: { viralPotential: 45 },
      viralPatterns: { score: 35 },
      hookEffectiveness: 40,
      overallViralScore: 42,
      riskFactors: ['Analysis failed'],
      strengthFactors: ['Unknown'],
    };
  }
}

/**
 * Author Influence Calculation
 */
function calculateAuthorInfluence(input: ViralAnalysisInput): number {
  let score = 0;
  
  // Follower count impact (logarithmic scale)
  const followers = input.authorFollowerCount || 0;
  if (followers > 0) {
    score += Math.min(40, Math.log10(followers) * 8);
  }
  
  // Verification bonus
  if (input.authorIsVerified) {
    score += 25;
  }
  
  // Recent performance bonus
  if (input.context?.authorRecentPerformance?.length) {
    const avgPerformance = input.context.authorRecentPerformance.reduce((a, b) => a + b, 0) / input.context.authorRecentPerformance.length;
    score += Math.min(35, avgPerformance / 100);
  }
  
  return Math.min(100, score);
}

/**
 * Timing Factors Analysis
 */
function analyzeTimingFactors(input: ViralAnalysisInput) {
  const now = Date.now();
  const postTime = input.timePosted || now;
  const hoursAgo = (now - postTime) / (1000 * 60 * 60);
  
  let timingScore = 50; // Base score
  
  // Recency bonus (viral content often spreads quickly)
  if (hoursAgo < 1) {
    timingScore += 30; // Very fresh content
  } else if (hoursAgo < 4) {
    timingScore += 20; // Recent content
  } else if (hoursAgo < 12) {
    timingScore += 10; // Same day
  } else if (hoursAgo > 48) {
    timingScore -= 20; // Old content less likely to go viral
  }
  
  // Time of day factors (simplified - in production, would use timezone)
  const hour = new Date(postTime).getHours();
  if (hour >= 9 && hour <= 11) {
    timingScore += 10; // Morning engagement peak
  } else if (hour >= 15 && hour <= 17) {
    timingScore += 15; // Afternoon peak
  } else if (hour >= 19 && hour <= 21) {
    timingScore += 20; // Evening peak
  }
  
  return {
    timingScore: Math.max(0, Math.min(100, timingScore)),
    hoursAgo,
    postedAtPeakTime: hour >= 9 && hour <= 21,
  };
}

/**
 * Trend Alignment Analysis
 */
async function analyzeTrendAlignment(input: ViralAnalysisInput) {
  let alignmentScore = 50; // Base score
  
  // Check if content mentions trending topics
  if (input.context?.trendingTopics?.length) {
    const contentLower = input.content.toLowerCase();
    const mentionedTrends = input.context.trendingTopics.filter(topic => 
      contentLower.includes(topic.toLowerCase())
    );
    
    if (mentionedTrends.length > 0) {
      alignmentScore += Math.min(30, mentionedTrends.length * 15);
    }
  }
  
  // Check hashtag relevance
  if (input.hashtags?.length) {
    // Popular hashtag patterns
    const popularPatterns = ['#breaking', '#viral', '#trending', '#news', '#update'];
    const matchingPatterns = input.hashtags.filter(tag => 
      popularPatterns.some(pattern => tag.toLowerCase().includes(pattern.slice(1)))
    );
    
    if (matchingPatterns.length > 0) {
      alignmentScore += 15;
    }
  }
  
  return {
    alignmentScore: Math.min(100, alignmentScore),
    mentionsTrends: (input.context?.trendingTopics?.length || 0) > 0,
    hasViralHashtags: (input.hashtags?.length || 0) > 0,
  };
}

/**
 * Emotional Impact Analysis
 */
async function analyzeEmotionalImpact(input: ViralAnalysisInput) {
  const client = getOpenRouterClient();
  
  const emotionalPrompt = `
Analyze the emotional impact and viral triggers in this tweet:

"${input.content}"

Score the following emotional factors (0-100):
1. Emotional intensity (how strongly it makes people feel)
2. Relatability (how many people can connect with this)
3. Surprise factor (how unexpected or novel the content is)
4. Inspiration level (how uplifting or motivating it is)
5. Controversy potential (how likely to spark debate)
6. Humor factor (how funny or entertaining it is)

Provide response in JSON format:
{
  "emotionalIntensity": number,
  "relatability": number,
  "surpriseFactor": number,
  "inspirationLevel": number,
  "controversyPotential": number,
  "humorFactor": number,
  "dominantEmotion": "joy" | "surprise" | "anger" | "fear" | "sadness" | "trust",
  "impactScore": number (0-100),
  "viralEmotions": [string]
}`;

  try {
    const response = await client.generateCompletion(emotionalPrompt, {
      model: 'google/gemini-2.0-flash-exp:free',
      systemPrompt: 'You are an expert in emotional psychology and viral content analysis.',
      maxTokens: 300,
      temperature: 0.4,
    });
    
    return JSON.parse(response.content);
  } catch (error) {
    console.error('Emotional analysis failed:', error);
    return {
      impactScore: 50,
      dominantEmotion: 'neutral',
      viralEmotions: ['unknown'],
    };
  }
}

/**
 * Shareability Calculation
 */
function calculateShareability(input: ViralAnalysisInput): number {
  let score = 40; // Base shareability
  
  // Content length optimization
  const contentLength = input.content.length;
  if (contentLength >= 50 && contentLength <= 180) {
    score += 20; // Optimal length for sharing
  } else if (contentLength <= 280) {
    score += 10; // Good length
  }
  
  // Media bonus (images/videos increase shareability)
  if (input.hasMedia) {
    score += 25;
  }
  
  // Hashtag optimization
  const hashtagCount = input.hashtags?.length || 0;
  if (hashtagCount >= 1 && hashtagCount <= 3) {
    score += 15; // Optimal hashtag count
  } else if (hashtagCount > 3) {
    score -= 5; // Too many hashtags
  }
  
  // Mention optimization
  const mentionCount = input.mentions?.length || 0;
  if (mentionCount >= 1 && mentionCount <= 2) {
    score += 10; // Good engagement potential
  }
  
  return Math.min(100, score);
}

/**
 * Build Final Viral Prediction
 */
function buildViralPrediction(factors: {
  contentQuality: number;
  authorInfluence: number;
  timingBonus: number;
  trendAlignment: number;
  emotionalImpact: number;
  shareability: number;
}, input: ViralAnalysisInput): ViralPrediction {
  // Weighted viral probability calculation
  const weights = {
    contentQuality: 0.25,
    authorInfluence: 0.20,
    timingBonus: 0.15,
    trendAlignment: 0.15,
    emotionalImpact: 0.15,
    shareability: 0.10,
  };
  
  const viralProbability = Object.entries(weights).reduce((prob, [factor, weight]) => {
    return prob + (factors[factor as keyof typeof factors] / 100) * weight;
  }, 0);
  
  // Predict engagement based on current metrics and viral probability
  const baseEngagement = input.currentEngagement || { likes: 0, retweets: 0, replies: 0 };
  const multiplier = 1 + (viralProbability * 10); // 1x to 11x multiplier
  
  const predictedEngagement = {
    likes: Math.round(baseEngagement.likes * multiplier),
    retweets: Math.round(baseEngagement.retweets * multiplier),
    replies: Math.round(baseEngagement.replies * multiplier),
    peakTime: viralProbability > 0.7 ? 2 : viralProbability > 0.5 ? 6 : 12,
  };
  
  // Generate action recommendations
  const actionRecommendations = generateActionRecommendations(viralProbability, factors);
  
  // Calculate confidence level
  const confidenceLevel = calculateConfidenceLevel(factors, input);
  
  // Identify risk factors
  const riskFactors = identifyRiskFactors(factors, input);
  
  return {
    viralProbability: Math.min(0.99, Math.max(0.01, viralProbability)),
    predictedEngagement,
    viralFactors: factors,
    actionRecommendations,
    confidenceLevel,
    riskFactors,
  };
}

/**
 * Generate Action Recommendations
 */
function generateActionRecommendations(viralProbability: number, factors: Record<string, number>): string[] {
  const recommendations = [];
  
  if (viralProbability > 0.8) {
    recommendations.push('🚀 High viral potential - respond immediately for maximum exposure');
    recommendations.push('📈 Consider promoted response to amplify reach');
  } else if (viralProbability > 0.6) {
    recommendations.push('⚡ Good viral potential - prioritize quick response');
    recommendations.push('🎯 Craft high-quality response to ride the wave');
  } else if (viralProbability > 0.4) {
    recommendations.push('💡 Moderate potential - create thoughtful response');
    recommendations.push('⏰ Monitor engagement velocity for 1-2 hours');
  } else {
    recommendations.push('📝 Standard response approach recommended');
    recommendations.push('🔍 Focus on quality over speed');
  }
  
  // Factor-specific recommendations
  if (factors.authorInfluence > 80) {
    recommendations.push('⭐ High-influence author - engagement guaranteed');
  }
  
  if (factors.emotionalImpact > 70) {
    recommendations.push('❤️ High emotional impact - expect strong reactions');
  }
  
  if (factors.trendAlignment > 70) {
    recommendations.push('📊 Trending topic alignment - maximize trend keywords');
  }
  
  return recommendations;
}

/**
 * Calculate Confidence Level
 */
function calculateConfidenceLevel(factors: Record<string, number>, input: ViralAnalysisInput): number {
  let confidence = 0.5; // Base confidence
  
  // More data = higher confidence
  if (input.authorFollowerCount) confidence += 0.1;
  if (input.currentEngagement) confidence += 0.1;
  if (input.context?.trendingTopics?.length) confidence += 0.1;
  if (input.hasMedia !== undefined) confidence += 0.05;
  if (input.hashtags?.length) confidence += 0.05;
  
  // Factor consistency increases confidence
  const factorValues = Object.values(factors).filter(v => typeof v === 'number');
  const factorVariance = calculateVariance(factorValues);
  if (factorVariance < 500) confidence += 0.1; // Low variance = consistent prediction
  
  return Math.min(0.99, Math.max(0.1, confidence));
}

/**
 * Identify Risk Factors
 */
function identifyRiskFactors(factors: Record<string, number>, input: ViralAnalysisInput): string[] {
  const risks = [];
  
  if (factors.contentQuality < 40) {
    risks.push('Low content quality may limit viral potential');
  }
  
  if (factors.authorInfluence < 30) {
    risks.push('Limited author influence reduces reach probability');
  }
  
  if (factors.emotionalImpact > 80 && factors.emotionalImpact < 90) {
    risks.push('High emotional content may trigger negative reactions');
  }
  
  if (!input.hasMedia && factors.shareability < 50) {
    risks.push('No media content reduces shareability');
  }
  
  const hoursOld = input.timePosted ? (Date.now() - input.timePosted) / (1000 * 60 * 60) : 0;
  if (hoursOld > 24) {
    risks.push('Content age reduces viral potential');
  }
  
  return risks;
}

/**
 * Helper Functions
 */
function calculateVariance(numbers: number[]): number {
  const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
  const squareDiffs = numbers.map(value => Math.pow(value - mean, 2));
  return squareDiffs.reduce((a, b) => a + b, 0) / numbers.length;
}

function generateFallbackPrediction(input: ViralAnalysisInput): ViralPrediction {
  const baseProb = 0.3; // Conservative baseline
  
  return {
    viralProbability: baseProb,
    predictedEngagement: {
      likes: (input.currentEngagement?.likes || 10) * 2,
      retweets: (input.currentEngagement?.retweets || 5) * 2,
      replies: (input.currentEngagement?.replies || 3) * 2,
      peakTime: 8,
    },
    viralFactors: {
      contentQuality: 50,
      authorInfluence: input.authorFollowerCount ? Math.min(80, Math.log10(input.authorFollowerCount) * 10) : 30,
      timingBonus: 40,
      trendAlignment: 45,
      emotionalImpact: 50,
      shareability: 45,
    },
    actionRecommendations: ['Standard response approach recommended'],
    confidenceLevel: 0.4,
    riskFactors: ['Limited data for accurate prediction'],
  };
}

/**
 * Get trending analytics for real-time dashboard
 */
export const getTrendingAnalytics = query({
  args: {
    timeRange: v.optional(v.union(v.literal("1h"), v.literal("6h"), v.literal("24h"))),
  },
  handler: async (ctx, args) => {
    const timeRange = args.timeRange || "6h";
    const hours = timeRange === "1h" ? 1 : timeRange === "6h" ? 6 : 24;
    const startTime = Date.now() - (hours * 60 * 60 * 1000);
    
    // Get recent mentions for viral analysis
    const recentMentions = await ctx.db
      .query("mentions")
      .withIndex("by_discovered_at")
      .filter(q => q.gte(q.field("discoveredAt"), startTime))
      .collect();
    
    // Calculate viral mentions (high engagement)
    const viralMentions = recentMentions.filter(mention => {
      const totalEngagement = mention.engagement.likes + mention.engagement.retweets + mention.engagement.replies;
      return totalEngagement > 50 || mention.priority === "high";
    }).length;
    
    // Extract trending topics from content
    const allContent = recentMentions.map(m => m.mentionContent).join(' ');
    const hashtagMatches = allContent.match(/#\w+/g) || [];
    const hashtags = [...new Set(hashtagMatches.map(tag => tag.toLowerCase()))];
    
    // Count hashtag frequency
    const hashtagCounts = hashtags.reduce((acc, hashtag) => {
      const count = (allContent.match(new RegExp(hashtag, 'gi')) || []).length;
      acc[hashtag] = count;
      return acc;
    }, {} as Record<string, number>);
    
    // Get top trending topics
    const topTopics = Object.entries(hashtagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([topic, count]) => ({ topic, count }));
    
    return {
      viralMentions,
      totalMentions: recentMentions.length,
      topTopics,
      timeRange,
      lastUpdated: Date.now(),
    };
  },
});

/**
 * Batch Viral Analysis for Multiple Tweets
 */
export const batchViralAnalysis = action({
  args: {
    inputs: v.array(v.object({
      content: v.string(),
      authorHandle: v.string(),
      authorFollowerCount: v.optional(v.number()),
      authorIsVerified: v.optional(v.boolean()),
      currentEngagement: v.optional(v.object({
        likes: v.number(),
        retweets: v.number(),
        replies: v.number(),
        views: v.optional(v.number()),
      })),
      timePosted: v.optional(v.number()),
      hasMedia: v.optional(v.boolean()),
      hashtags: v.optional(v.array(v.string())),
      mentions: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, { inputs }) => {
    const results = await Promise.allSettled(
      inputs.map(input => ctx.runAction(api.ai.viralDetection.predictViralPotential, { input }))
    );
    
    return results.map((result, index) => ({
      index,
      success: result.status === 'fulfilled',
      prediction: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null,
    }));
  },
});