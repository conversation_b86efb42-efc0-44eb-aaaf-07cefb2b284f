/**
 * Unified image generation using best available provider (OpenAI + Fal.ai)
 */
export declare const generateUnifiedImage: any;
/**
 * Generate social media optimized images with intelligent provider selection
 */
export declare const generateSocialImage: any;
/**
 * Generate multiple image variations using optimal provider mix
 */
export declare const generateUnifiedImageVariations: any;
/**
 * Advanced image generation for specific content types
 */
export declare const generateContentImage: any;
/**
 * Test all image generation providers and capabilities
 */
export declare const testUnifiedImageGeneration: any;
/**
 * Get image generation analytics and costs
 */
export declare const getImageGenerationAnalytics: any;
