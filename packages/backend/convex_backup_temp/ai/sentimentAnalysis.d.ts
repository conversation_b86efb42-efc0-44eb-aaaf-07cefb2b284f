/**
 * Advanced Sentiment Analysis for Financial/Trading Mentions
 * Provides bullish/bearish/neutral classification with confidence scores
 */
export interface SentimentAnalysisResult {
    sentiment: "bullish" | "bearish" | "neutral";
    sentimentScore: number;
    confidence: number;
    marketSentiment: {
        bullishScore: number;
        bearishScore: number;
        neutralScore: number;
        marketContext: string[];
    };
    emotions?: {
        excitement: number;
        fear: number;
        greed: number;
        fomo: number;
        panic: number;
    };
    reasoning: string;
    keyWords: string[];
    analysisModel: string;
    analyzedAt: number;
}
/**
 * Analyze mention sentiment for financial/trading context
 */
export declare const analyzeMentionSentiment: any;
/**
 * Batch analyze sentiment for multiple mentions
 */
export declare const batchAnalyzeSentiment: any;
