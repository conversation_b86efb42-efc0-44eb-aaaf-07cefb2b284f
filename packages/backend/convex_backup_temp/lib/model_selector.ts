/**
 * AI Model Selection Utility
 * 
 * Intelligently selects the appropriate AI models based on:
 * - Performance requirements (ultra_fast, fast, quality, bulk)
 * - Model type (text, image, video, embedding, analysis)
 * - Environment configuration
 * - Cost optimization settings
 * - Current system load
 */

export type ModelStrategy = 'ultra_fast' | 'fast' | 'quality' | 'bulk' | 'auto';
export type ModelType = 'text' | 'image' | 'video' | 'embedding' | 'analysis';

export interface ModelSelection {
  primary: string;
  backup?: string;
  fallbacks: string[];
  timeout: number;
  maxRetries: number;
  strategy: ModelStrategy;
  estimatedCost: number; // in cents
}

export interface ModelUsageContext {
  userTier?: 'free' | 'premium' | 'enterprise';
  urgency?: 'low' | 'medium' | 'high' | 'critical';
  quality?: 'draft' | 'standard' | 'high' | 'premium';
  batchSize?: number;
  isBusinessHours?: boolean;
  systemLoad?: 'low' | 'medium' | 'high';
}

/**
 * Advanced model selector with intelligent strategy selection
 */
export class ModelSelector {
  private static instance: ModelSelector;

  private constructor() {}

  static getInstance(): ModelSelector {
    if (!ModelSelector.instance) {
      ModelSelector.instance = new ModelSelector();
    }
    return ModelSelector.instance;
  }

  /**
   * Select the best model based on strategy and context
   */
  selectModel(
    type: ModelType,
    strategy?: ModelStrategy,
    context?: ModelUsageContext
  ): ModelSelection {
    const finalStrategy = this.determineStrategy(type, strategy, context);
    
    switch (type) {
      case 'text':
        return this.selectTextModel(finalStrategy, context);
      case 'image':
        return this.selectImageModel(finalStrategy, context);
      case 'video':
        return this.selectVideoModel(finalStrategy, context);
      case 'embedding':
        return this.selectEmbeddingModel(finalStrategy, context);
      case 'analysis':
        return this.selectAnalysisModel(finalStrategy, context);
      default:
        throw new Error(`Unsupported model type: ${type}`);
    }
  }

  /**
   * Determine the optimal strategy based on context
   */
  private determineStrategy(
    type: ModelType,
    requestedStrategy?: ModelStrategy,
    context?: ModelUsageContext
  ): ModelStrategy {
    // If strategy is explicitly requested, use it
    if (requestedStrategy && requestedStrategy !== 'auto') {
      return requestedStrategy;
    }

    // Get default strategy from environment
    let defaultStrategy: ModelStrategy = 'fast';
    switch (type) {
      case 'text':
        defaultStrategy = (process.env.DEFAULT_TEXT_STRATEGY as ModelStrategy) || 'fast';
        break;
      case 'image':
        defaultStrategy = (process.env.DEFAULT_IMAGE_STRATEGY as ModelStrategy) || 'quality';
        break;
      case 'embedding':
        defaultStrategy = (process.env.DEFAULT_EMBEDDING_STRATEGY as ModelStrategy) || 'fast';
        break;
      case 'analysis':
        defaultStrategy = (process.env.DEFAULT_ANALYSIS_STRATEGY as ModelStrategy) || 'fast';
        break;
      case 'video':
        defaultStrategy = 'quality'; // Videos typically need quality
        break;
    }

    // If auto strategy, determine based on context
    if (requestedStrategy === 'auto' && context) {
      return this.autoSelectStrategy(defaultStrategy, context);
    }

    return defaultStrategy;
  }

  /**
   * Auto-select strategy based on intelligent context analysis
   */
  private autoSelectStrategy(
    defaultStrategy: ModelStrategy,
    context: ModelUsageContext
  ): ModelStrategy {
    // Critical urgency always gets ultra_fast
    if (context.urgency === 'critical') {
      return 'ultra_fast';
    }

    // High system load should prefer faster models
    if (context.systemLoad === 'high') {
      return 'ultra_fast';
    }

    // Premium quality requests get quality models
    if (context.quality === 'premium') {
      return 'quality';
    }

    // Batch processing gets bulk models
    if (context.batchSize && context.batchSize > 10) {
      return 'bulk';
    }

    // Business hours strategy from env
    if (context.isBusinessHours !== undefined) {
      const businessStrategy = process.env.AUTO_STRATEGY_BUSINESS_HOURS as ModelStrategy;
      const offHoursStrategy = process.env.AUTO_STRATEGY_OFF_HOURS as ModelStrategy;
      
      if (context.isBusinessHours && businessStrategy) {
        return businessStrategy;
      }
      if (!context.isBusinessHours && offHoursStrategy) {
        return offHoursStrategy;
      }
    }

    // Free tier users get fast models
    if (context.userTier === 'free') {
      return 'fast';
    }

    // Enterprise tier gets quality
    if (context.userTier === 'enterprise') {
      return 'quality';
    }

    return defaultStrategy;
  }

  /**
   * Select text generation model
   */
  private selectTextModel(strategy: ModelStrategy, context?: ModelUsageContext): ModelSelection {
    const costOptimization = process.env.ENABLE_COST_OPTIMIZATION === 'true';
    const maxCost = parseInt(process.env.MAX_COST_PER_TEXT_REQUEST || '5');

    switch (strategy) {
      case 'ultra_fast':
        return {
          primary: process.env.TEXT_MODEL_ULTRA_FAST || 'google/gemini-2.5-flash-preview-05-20',
          backup: process.env.TEXT_MODEL_ULTRA_FAST_BACKUP || 'anthropic/claude-3.5-haiku',
          fallbacks: this.parseFallbacks(process.env.TEXT_MODELS_FALLBACK, [
            'google/gemini-flash-1.5-8b:free',
            'meta-llama/llama-3.1-8b-instruct:free'
          ]),
          timeout: parseInt(process.env.ULTRA_FAST_TIMEOUT || '2000'),
          maxRetries: parseInt(process.env.MAX_RETRIES_ULTRA_FAST || '1'),
          strategy,
          estimatedCost: 0.05, // Gemini 2.5 Flash is very cost-effective
        };

      case 'fast':
        return {
          primary: process.env.TEXT_MODEL_FAST || 'google/gemini-2.5-flash-preview-05-20',
          backup: process.env.TEXT_MODEL_FAST_BACKUP || 'openai/gpt-4o-mini',
          fallbacks: this.parseFallbacks(process.env.TEXT_MODELS_FALLBACK, [
            'google/gemini-pro-1.5',
            'meta-llama/llama-3.1-8b-instruct'
          ]),
          timeout: parseInt(process.env.FAST_TIMEOUT || '8000'),
          maxRetries: parseInt(process.env.MAX_RETRIES_FAST || '2'),
          strategy,
          estimatedCost: 0.1, // Gemini 2.5 Flash is cost-effective
        };

      case 'quality':
        return {
          primary: process.env.TEXT_MODEL_QUALITY || 'google/gemini-2.5-pro-preview',
          backup: process.env.TEXT_MODEL_QUALITY_BACKUP || 'anthropic/claude-3.5-sonnet',
          fallbacks: this.parseFallbacks(process.env.TEXT_MODELS_FALLBACK, [
            'anthropic/claude-3.5-haiku',
            'openai/gpt-4o-mini'
          ]),
          timeout: parseInt(process.env.QUALITY_TIMEOUT || '30000'),
          maxRetries: parseInt(process.env.MAX_RETRIES_QUALITY || '3'),
          strategy,
          estimatedCost: 2.0, // Gemini 2.5 Pro is more affordable than Claude/GPT-4
        };

      case 'bulk':
        return {
          primary: process.env.TEXT_MODEL_BULK || 'google/gemini-flash-1.5-8b:free',
          backup: process.env.TEXT_MODEL_BULK_BACKUP || 'meta-llama/llama-3.1-8b-instruct:free',
          fallbacks: this.parseFallbacks(process.env.TEXT_MODELS_FALLBACK, [
            'google/gemini-2.0-flash-exp:free',
            'mistralai/mistral-7b-instruct:free'
          ]),
          timeout: parseInt(process.env.BULK_TIMEOUT || '60000'),
          maxRetries: parseInt(process.env.MAX_RETRIES_BULK || '1'),
          strategy,
          estimatedCost: 0.05,
        };

      default:
        return this.selectTextModel('fast', context);
    }
  }

  /**
   * Select image generation model
   */
  private selectImageModel(strategy: ModelStrategy, context?: ModelUsageContext): ModelSelection {
    switch (strategy) {
      case 'ultra_fast':
      case 'fast':
        return {
          primary: process.env.IMAGE_MODEL_FAST || 'dall-e-2',
          backup: process.env.IMAGE_MODEL_FAST_BACKUP || 'fal-ai/flux-pro',
          fallbacks: this.parseFallbacks(process.env.IMAGE_MODELS_FALLBACK, ['dall-e-2']),
          timeout: 15000,
          maxRetries: 2,
          strategy,
          estimatedCost: 0.020, // DALL-E 2 is cost-effective
        };

      case 'quality':
        return {
          primary: process.env.IMAGE_MODEL_QUALITY || 'fal-ai/flux-pro',
          backup: process.env.IMAGE_MODEL_QUALITY_BACKUP || 'dall-e-3',
          fallbacks: this.parseFallbacks(process.env.IMAGE_MODELS_FALLBACK, [
            'dall-e-2',
            'dall-e-3'
          ]),
          timeout: 30000,
          maxRetries: 3,
          strategy,
          estimatedCost: 0.055, // Fal.ai Flux Pro pricing
        };

      case 'bulk':
        return {
          primary: process.env.IMAGE_MODEL_BULK || 'dall-e-2',
          backup: process.env.IMAGE_MODEL_BULK_BACKUP || 'fal-ai/flux-pro',
          fallbacks: this.parseFallbacks(process.env.IMAGE_MODELS_FALLBACK, ['dall-e-2']),
          timeout: 45000,
          maxRetries: 1,
          strategy,
          estimatedCost: 0.018, // Bulk pricing for DALL-E 2
        };

      default:
        return this.selectImageModel('quality', context);
    }
  }

  /**
   * Select video generation model
   */
  private selectVideoModel(strategy: ModelStrategy, context?: ModelUsageContext): ModelSelection {
    return {
      primary: process.env.VIDEO_MODEL_PRIMARY || 'openai/sora',
      backup: process.env.VIDEO_MODEL_PRIMARY_BACKUP || 'runwayml/gen-2',
      fallbacks: this.parseFallbacks(process.env.VIDEO_MODELS_FALLBACK, [
        'runwayml/gen-2',
        'stability-ai/stable-video-diffusion'
      ]),
      timeout: 120000, // Videos take longer
      maxRetries: 2,
      strategy,
      estimatedCost: 100,
    };
  }

  /**
   * Select embedding model
   */
  private selectEmbeddingModel(strategy: ModelStrategy, context?: ModelUsageContext): ModelSelection {
    switch (strategy) {
      case 'ultra_fast':
      case 'fast':
        return {
          primary: process.env.EMBEDDING_MODEL_FAST || 'text-embedding-3-small',
          backup: process.env.EMBEDDING_MODEL_FAST_BACKUP || 'text-embedding-ada-002',
          fallbacks: this.parseFallbacks(process.env.EMBEDDING_MODELS_FALLBACK, [
            'text-embedding-ada-002'
          ]),
          timeout: 5000,
          maxRetries: 2,
          strategy,
          estimatedCost: 0.02,
        };

      case 'quality':
        return {
          primary: process.env.EMBEDDING_MODEL_QUALITY || 'text-embedding-3-large',
          backup: process.env.EMBEDDING_MODEL_QUALITY_BACKUP || 'text-embedding-3-small',
          fallbacks: this.parseFallbacks(process.env.EMBEDDING_MODELS_FALLBACK, [
            'text-embedding-3-small',
            'text-embedding-ada-002'
          ]),
          timeout: 10000,
          maxRetries: 3,
          strategy,
          estimatedCost: 0.1,
        };

      case 'bulk':
        return {
          primary: process.env.EMBEDDING_MODEL_BULK || 'text-embedding-3-small',
          backup: process.env.EMBEDDING_MODEL_BULK_BACKUP || 'text-embedding-ada-002',
          fallbacks: this.parseFallbacks(process.env.EMBEDDING_MODELS_FALLBACK, [
            'text-embedding-ada-002'
          ]),
          timeout: 30000,
          maxRetries: 1,
          strategy,
          estimatedCost: 0.01,
        };

      default:
        return this.selectEmbeddingModel('fast', context);
    }
  }

  /**
   * Select analysis model
   */
  private selectAnalysisModel(strategy: ModelStrategy, context?: ModelUsageContext): ModelSelection {
    switch (strategy) {
      case 'ultra_fast':
      case 'fast':
        return {
          primary: process.env.ANALYSIS_MODEL_FAST || 'google/gemini-2.0-flash-exp:free',
          backup: process.env.ANALYSIS_MODEL_FAST_BACKUP || 'google/gemini-flash-1.5-8b:free',
          fallbacks: this.parseFallbacks(process.env.ANALYSIS_MODELS_FALLBACK, [
            'anthropic/claude-3.5-haiku',
            'google/gemini-flash-1.5-8b:free'
          ]),
          timeout: 8000,
          maxRetries: 2,
          strategy,
          estimatedCost: 0.05,
        };

      case 'quality':
        return {
          primary: process.env.ANALYSIS_MODEL_QUALITY || 'google/gemini-pro-1.5',
          backup: process.env.ANALYSIS_MODEL_QUALITY_BACKUP || 'anthropic/claude-3.5-haiku',
          fallbacks: this.parseFallbacks(process.env.ANALYSIS_MODELS_FALLBACK, [
            'anthropic/claude-3.5-haiku',
            'google/gemini-flash-1.5-8b:free'
          ]),
          timeout: 20000,
          maxRetries: 3,
          strategy,
          estimatedCost: 1.0,
        };

      case 'bulk':
        return {
          primary: process.env.ANALYSIS_MODEL_BULK || 'google/gemini-flash-1.5-8b:free',
          backup: process.env.ANALYSIS_MODEL_BULK_BACKUP || 'meta-llama/llama-3.1-8b-instruct:free',
          fallbacks: this.parseFallbacks(process.env.ANALYSIS_MODELS_FALLBACK, [
            'google/gemini-2.0-flash-exp:free',
            'anthropic/claude-3.5-haiku'
          ]),
          timeout: 45000,
          maxRetries: 1,
          strategy,
          estimatedCost: 0.01,
        };

      default:
        return this.selectAnalysisModel('fast', context);
    }
  }

  /**
   * Parse fallback models from environment string
   */
  private parseFallbacks(envValue?: string, defaults: string[] = []): string[] {
    if (!envValue) return defaults;
    return envValue.split(',').map(s => s.trim()).filter(Boolean);
  }

  /**
   * Get current system context
   */
  getCurrentContext(): ModelUsageContext {
    const hour = new Date().getHours();
    const isBusinessHours = hour >= 9 && hour <= 17;

    return {
      isBusinessHours,
      systemLoad: 'medium', // Could be enhanced with actual metrics
    };
  }

  /**
   * Check if cost optimization is enabled and model fits budget
   */
  isWithinBudget(selection: ModelSelection, type: ModelType): boolean {
    if (process.env.ENABLE_COST_OPTIMIZATION !== 'true') {
      return true;
    }

    let maxCost = 5; // default
    switch (type) {
      case 'text':
        maxCost = parseInt(process.env.MAX_COST_PER_TEXT_REQUEST || '5');
        break;
      case 'image':
        maxCost = parseInt(process.env.MAX_COST_PER_IMAGE_REQUEST || '20');
        break;
      case 'video':
        maxCost = parseInt(process.env.MAX_COST_PER_VIDEO_REQUEST || '100');
        break;
      case 'embedding':
        maxCost = 1; // Embeddings are generally cheap
        break;
      case 'analysis':
        maxCost = 2; // Analysis is generally cheaper than full generation
        break;
    }

    return selection.estimatedCost <= maxCost;
  }

  /**
   * Get model selection with budget constraints
   */
  selectModelWithBudget(
    type: ModelType,
    strategy?: ModelStrategy,
    context?: ModelUsageContext
  ): ModelSelection {
    let selection = this.selectModel(type, strategy, context);

    // If over budget and cost optimization is enabled, try cheaper strategy
    if (!this.isWithinBudget(selection, type) && process.env.ENABLE_COST_OPTIMIZATION === 'true') {
      if (selection.strategy !== 'bulk') {
        selection = this.selectModel(type, 'bulk', context);
      }
      
      // If still over budget and fallback to free is enabled, use free models
      if (!this.isWithinBudget(selection, type) && process.env.FALLBACK_TO_FREE_ON_BUDGET_LIMIT === 'true') {
        selection.primary = this.getFreeModel(type);
        selection.estimatedCost = 0;
      }
    }

    return selection;
  }

  /**
   * Get free/cheap model for budget constraints
   */
  private getFreeModel(type: ModelType): string {
    switch (type) {
      case 'text':
        return 'google/gemini-flash-1.5-8b:free';
      case 'image':
        return 'stability-ai/stable-diffusion-xl';
      case 'video':
        return 'stability-ai/stable-video-diffusion';
      case 'embedding':
        return 'text-embedding-ada-002';
      case 'analysis':
        return 'google/gemini-2.0-flash-exp:free';
      default:
        return 'google/gemini-flash-1.5-8b:free';
    }
  }
}

/**
 * Convenience functions for common use cases
 */
export function selectTextModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget('text', strategy, context);
}

export function selectImageModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget('image', strategy, context);
}

export function selectEmbeddingModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget('embedding', strategy, context);
}

export function selectAnalysisModel(strategy?: ModelStrategy, context?: ModelUsageContext): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget('analysis', strategy, context);
}

/**
 * Get optimal model for real-time responses
 */
export function getRealtimeModel(type: ModelType): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget(type, 'ultra_fast', {
    urgency: 'high',
    quality: 'standard',
  });
}

/**
 * Get optimal model for high-quality results
 */
export function getQualityModel(type: ModelType): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget(type, 'quality', {
    quality: 'premium',
    urgency: 'low',
  });
}

/**
 * Get optimal model for batch processing
 */
export function getBulkModel(type: ModelType, batchSize: number): ModelSelection {
  return ModelSelector.getInstance().selectModelWithBudget(type, 'bulk', {
    batchSize,
    quality: 'standard',
    urgency: 'low',
  });
}