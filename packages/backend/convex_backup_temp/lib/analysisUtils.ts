/**
 * Analysis utilities for tweet processing and validation
 */

export interface AnalysisStructure {
  worthinessScore: number;
  shouldRespond: boolean;
  responseStrategy?: string;
  reasoning?: string;
  topics?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
  urgency?: 'low' | 'medium' | 'high';
  engagement?: {
    likes: number;
    retweets: number;
    replies: number;
  };
  enhancedScore?: number;
  semanticRelevance?: number;
}

/**
 * Validate and sanitize analysis structure from AI response
 */
export function validateAnalysisStructure(analysis: any): AnalysisStructure {
  const validated: AnalysisStructure = {
    worthinessScore: typeof analysis.worthinessScore === 'number' 
      ? Math.min(Math.max(analysis.worthinessScore, 0), 1)
      : 0.5,
    shouldRespond: <PERSON><PERSON><PERSON>(analysis.shouldRespond),
    responseStrategy: typeof analysis.responseStrategy === 'string' 
      ? analysis.responseStrategy 
      : undefined,
    reasoning: typeof analysis.reasoning === 'string' 
      ? analysis.reasoning.substring(0, 500) // Limit length
      : undefined,
    topics: Array.isArray(analysis.topics) 
      ? analysis.topics.filter((t: any) => typeof t === 'string').slice(0, 10)
      : [],
    sentiment: ['positive', 'negative', 'neutral'].includes(analysis.sentiment)
      ? analysis.sentiment
      : 'neutral',
    urgency: ['low', 'medium', 'high'].includes(analysis.urgency)
      ? analysis.urgency
      : 'low',
  };

  // Validate engagement object if present
  if (analysis.engagement && typeof analysis.engagement === 'object') {
    validated.engagement = {
      likes: typeof analysis.engagement.likes === 'number' ? Math.max(0, analysis.engagement.likes) : 0,
      retweets: typeof analysis.engagement.retweets === 'number' ? Math.max(0, analysis.engagement.retweets) : 0,
      replies: typeof analysis.engagement.replies === 'number' ? Math.max(0, analysis.engagement.replies) : 0,
    };
  }

  return validated;
}

/**
 * Create fallback analysis when AI response parsing fails
 */
export function createFallbackAnalysis(
  tweet: any, 
  enhancedScore?: number, 
  semanticScore?: number
): AnalysisStructure {
  const contentLength = tweet.content?.length || 0;
  const hasQuestion = tweet.content?.includes('?') || false;
  const hasHashtag = /#\w+/.test(tweet.content || '') || false;
  
  // Simple heuristic-based scoring
  let worthinessScore = 0.3; // Base score
  
  if (contentLength > 50 && contentLength < 200) worthinessScore += 0.2;
  if (hasQuestion) worthinessScore += 0.2;
  if (tweet.authorIsVerified) worthinessScore += 0.1;
  if (tweet.engagement?.likes > 5) worthinessScore += 0.1;
  if (hasHashtag) worthinessScore += 0.1;
  
  worthinessScore = Math.min(worthinessScore, 1);
  
  const analysis: AnalysisStructure = {
    worthinessScore,
    shouldRespond: worthinessScore > 0.6,
    responseStrategy: worthinessScore > 0.7 ? 'engage' : 'monitor',
    reasoning: 'Generated using fallback heuristics due to AI parsing failure',
    topics: hasHashtag ? [tweet.content.match(/#\w+/)?.[0]?.substring(1) || ''].filter(Boolean) : [],
    sentiment: 'neutral',
    urgency: worthinessScore > 0.8 ? 'high' : worthinessScore > 0.6 ? 'medium' : 'low',
    engagement: tweet.engagement || { likes: 0, retweets: 0, replies: 0 },
  };
  
  if (enhancedScore !== undefined) {
    analysis.enhancedScore = enhancedScore;
  }
  
  if (semanticScore !== undefined) {
    analysis.semanticRelevance = semanticScore;
  }
  
  return analysis;
}

/**
 * Extract key insights from content for analysis
 */
export function extractContentInsights(content: string): {
  topics: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  hasQuestion: boolean;
  hasCall2Action: boolean;
  urgencyIndicators: string[];
} {
  const topics: string[] = [];
  let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
  
  // Extract hashtags as topics
  const hashtags = content.match(/#\w+/g);
  if (hashtags) {
    topics.push(...hashtags.map(tag => tag.substring(1).toLowerCase()));
  }
  
  // Basic sentiment analysis
  const positiveWords = ['great', 'awesome', 'amazing', 'excellent', 'fantastic', 'love', 'best'];
  const negativeWords = ['terrible', 'awful', 'hate', 'worst', 'bad', 'disappointing', 'frustrated'];
  
  const lowerContent = content.toLowerCase();
  const positiveCount = positiveWords.filter(word => lowerContent.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerContent.includes(word)).length;
  
  if (positiveCount > negativeCount) {
    sentiment = 'positive';
  } else if (negativeCount > positiveCount) {
    sentiment = 'negative';
  }
  
  // Check for questions and call-to-actions
  const hasQuestion = content.includes('?');
  const hasCall2Action = /\b(check out|visit|buy now|sign up|register|join|follow)\b/i.test(content);
  
  // Urgency indicators
  const urgencyKeywords = ['urgent', 'asap', 'immediately', 'breaking', 'alert', 'emergency'];
  const urgencyIndicators = urgencyKeywords.filter(keyword => 
    lowerContent.includes(keyword)
  );
  
  return {
    topics,
    sentiment,
    hasQuestion,
    hasCall2Action,
    urgencyIndicators
  };
}

/**
 * Score content quality based on various factors
 */
export function scoreContentQuality(content: string, engagement?: any): number {
  let score = 0;
  
  // Length scoring (optimal range)
  const length = content.length;
  if (length >= 50 && length <= 200) {
    score += 0.3;
  } else if (length > 200 && length <= 280) {
    score += 0.2;
  } else if (length < 50) {
    score += 0.1;
  }
  
  // Content features
  const insights = extractContentInsights(content);
  
  if (insights.hasQuestion) score += 0.2;
  if (insights.hasCall2Action) score += 0.15;
  if (insights.topics.length > 0) score += 0.1;
  if (insights.sentiment === 'positive') score += 0.1;
  
  // Engagement bonus
  if (engagement) {
    const totalEngagement = (engagement.likes || 0) + 
                           (engagement.retweets || 0) + 
                           (engagement.replies || 0);
    if (totalEngagement > 0) {
      score += Math.min(totalEngagement / 50, 0.15); // Cap at 0.15
    }
  }
  
  return Math.min(score, 1); // Cap at 1.0
}