/**
 * Production-Grade Rate Limiting System
 * 🔐 SECURITY: Prevents API abuse, DDoS attacks, and quota exhaustion
 */
interface RateLimitConfig {
    maxRequests: number;
    windowMs: number;
    keyGenerator?: (ctx: any) => string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    message?: string;
}
/**
 * Creates a rate limiting middleware
 */
export declare function createRateLimit(config: RateLimitConfig): (ctx: any, operation: string) => Promise<void>;
/**
 * Pre-configured rate limiters for different operation types
 */
export declare const rateLimiters: {
    /**
     * Standard API operations
     */
    standard: (ctx: any, operation: string) => Promise<void>;
    /**
     * Expensive operations (AI calls, external APIs)
     */
    expensive: (ctx: any, operation: string) => Promise<void>;
    /**
     * xAI search operations with daily limits
     */
    xaiSearch: (ctx: any, operation: string) => Promise<void>;
    /**
     * Twitter API operations with hourly limits
     */
    twitterApi: (ctx: any, operation: string) => Promise<void>;
    /**
     * Authentication operations (more restrictive)
     */
    auth: (ctx: any, operation: string) => Promise<void>;
    /**
     * Data mutations (create, update, delete)
     */
    mutation: (ctx: any, operation: string) => Promise<void>;
};
/**
 * Decorator for applying rate limiting to Convex functions
 */
export declare function withRateLimit(limiter: ReturnType<typeof createRateLimit>, operation?: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * Helper to apply rate limiting in Convex handlers
 */
export declare function checkRateLimit(ctx: any, operation: string, type?: keyof typeof rateLimiters): Promise<void>;
/**
 * Get current rate limit status for a user
 */
export declare function getRateLimitStatus(ctx: any, operation: string, type?: keyof typeof rateLimiters): {
    remaining: number;
    resetTime: number;
    total: number;
};
/**
 * Reset rate limit for a specific user and operation (admin function)
 */
export declare function resetRateLimit(userId: string, operation: string): void;
export {};
