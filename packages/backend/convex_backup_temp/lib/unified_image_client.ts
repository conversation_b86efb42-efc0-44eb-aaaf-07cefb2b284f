/**
 * Unified Image Generation Client
 * Intelligently routes image generation requests between OpenAI and Fal.ai
 */

import { getOpenAIClient, type ImageGenerationResponse } from './openai_client';
import { getFalClient, type FalImageResponse } from './fal_client';
import { selectImageModel, type ModelStrategy, type ModelUsageContext } from './model_selector';

export type ImageProvider = 'openai' | 'fal' | 'auto';

export interface UnifiedImageRequest {
  prompt: string;
  provider?: ImageProvider;
  strategy?: ModelStrategy;
  context?: ModelUsageContext;
  
  // Common options
  size?: string;
  quality?: 'standard' | 'hd';
  style?: 'minimal' | 'vibrant' | 'professional' | 'artistic' | 'vivid' | 'natural';
  aspectRatio?: 'square' | 'landscape' | 'portrait';
  platform?: 'twitter' | 'instagram' | 'linkedin';
  
  // Provider-specific options
  openaiOptions?: {
    model?: 'dall-e-2' | 'dall-e-3';
    responseFormat?: 'url' | 'b64_json';
    user?: string;
  };
  
  falOptions?: {
    numInferenceSteps?: number;
    guidanceScale?: number;
    seed?: number;
    expandPrompt?: boolean;
    format?: 'jpeg' | 'png';
  };
}

export interface UnifiedImageResponse {
  url: string;
  provider: 'openai' | 'fal';
  model: string;
  width?: number;
  height?: number;
  contentType?: string;
  revisedPrompt?: string;
  seed?: number;
  inferenceTime?: number;
  estimatedCost: number;
  strategy: ModelStrategy;
  isFallback: boolean;
  generatedAt: number;
  usage?: {
    totalTokens?: number;
  };
}

/**
 * Unified image generation client with intelligent provider selection
 */
export class UnifiedImageClient {
  private static instance: UnifiedImageClient;

  private constructor() {}

  static getInstance(): UnifiedImageClient {
    if (!UnifiedImageClient.instance) {
      UnifiedImageClient.instance = new UnifiedImageClient();
    }
    return UnifiedImageClient.instance;
  }

  /**
   * Generate image with intelligent provider selection
   */
  async generateImage(request: UnifiedImageRequest): Promise<UnifiedImageResponse> {
    const startTime = Date.now();
    
    // Determine optimal provider and model
    const providerStrategy = this.selectProvider(request);
    
    let response: UnifiedImageResponse | null = null;
    let isFallback = false;

    // Try primary provider
    try {
      response = await this.generateWithProvider(request, providerStrategy.primary);
    } catch (error) {
      console.warn(`Primary provider ${providerStrategy.primary} failed:`, error);
      
      // Try fallback provider if available
      if (providerStrategy.fallback) {
        try {
          console.log(`🔄 Trying fallback provider: ${providerStrategy.fallback}`);
          response = await this.generateWithProvider(request, providerStrategy.fallback);
          isFallback = true;
        } catch (fallbackError) {
          console.error(`Fallback provider ${providerStrategy.fallback} failed:`, fallbackError);
        }
      }
    }

    if (!response) {
      throw new Error('All image generation providers failed');
    }

    return {
      ...response,
      isFallback,
      generatedAt: startTime,
    };
  }

  /**
   * Generate multiple image variations using optimal provider mix
   */
  async generateImageVariations(
    request: UnifiedImageRequest,
    count: number = 3
  ): Promise<UnifiedImageResponse[]> {
    const results: UnifiedImageResponse[] = [];
    const providers = this.getOptimalProviderMix(request, count);
    
    // Generate in parallel with different providers
    const promises = providers.map(async (provider, index) => {
      try {
        const variationRequest = {
          ...request,
          provider,
          falOptions: {
            ...request.falOptions,
            seed: request.falOptions?.seed ? request.falOptions.seed + index : undefined,
          },
        };
        
        return await this.generateImage(variationRequest);
      } catch (error) {
        console.error(`Variation ${index} with ${provider} failed:`, error);
        return null;
      }
    });

    const settled = await Promise.allSettled(promises);
    
    for (const result of settled) {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      }
    }

    if (results.length === 0) {
      throw new Error('All image variations failed to generate');
    }

    return results;
  }

  /**
   * Generate image with specific provider
   */
  private async generateWithProvider(
    request: UnifiedImageRequest,
    provider: ImageProvider
  ): Promise<Omit<UnifiedImageResponse, 'isFallback' | 'generatedAt'>> {
    const strategy = request.strategy || 'quality';
    
    if (provider === 'openai') {
      return this.generateWithOpenAI(request, strategy);
    } else if (provider === 'fal') {
      return this.generateWithFal(request, strategy);
    } else {
      throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Generate image using OpenAI DALL-E
   */
  private async generateWithOpenAI(
    request: UnifiedImageRequest,
    strategy: ModelStrategy
  ): Promise<Omit<UnifiedImageResponse, 'isFallback' | 'generatedAt'>> {
    const client = getOpenAIClient();
    
    // Convert unified request to OpenAI format
    const size = this.mapSizeToOpenAI(request.size, request.aspectRatio);
    const style = this.mapStyleToOpenAI(request.style);
    
    const response = await client.generateImage(request.prompt, {
      model: request.openaiOptions?.model || 'dall-e-3',
      size,
      quality: request.quality || 'hd',
      style,
      responseFormat: request.openaiOptions?.responseFormat || 'url',
      user: request.openaiOptions?.user,
    });

    return {
      url: response.url || '',
      provider: 'openai',
      model: response.model,
      revisedPrompt: response.revisedPrompt,
      estimatedCost: this.getOpenAICost(request.openaiOptions?.model || 'dall-e-3', size),
      strategy,
      usage: response.usage,
    };
  }

  /**
   * Generate image using Fal.ai Flux Pro
   */
  private async generateWithFal(
    request: UnifiedImageRequest,
    strategy: ModelStrategy
  ): Promise<Omit<UnifiedImageResponse, 'isFallback' | 'generatedAt'>> {
    const client = getFalClient();
    
    // Convert unified request to Fal format
    const imageSize = this.mapSizeToFal(request.size, request.aspectRatio);
    
    const response = await client.generateImage(request.prompt, {
      image_size: imageSize,
      num_inference_steps: this.getInferenceSteps(strategy),
      guidance_scale: request.falOptions?.guidanceScale || 3.5,
      seed: request.falOptions?.seed,
      expand_prompt: request.falOptions?.expandPrompt ?? true,
      format: request.falOptions?.format || 'jpeg',
    });

    return {
      url: response.url,
      provider: 'fal',
      model: response.model,
      width: response.width,
      height: response.height,
      contentType: response.contentType,
      revisedPrompt: response.revisedPrompt,
      seed: response.seed,
      inferenceTime: response.inferenceTime,
      estimatedCost: 0.055, // Fal.ai Flux Pro cost
      strategy,
    };
  }

  /**
   * Intelligent provider selection based on request characteristics
   */
  private selectProvider(request: UnifiedImageRequest): { primary: ImageProvider; fallback?: ImageProvider } {
    // If provider is explicitly specified, use it
    if (request.provider && request.provider !== 'auto') {
      const fallback = request.provider === 'openai' ? 'fal' : 'openai';
      return { primary: request.provider, fallback };
    }

    // Intelligent selection based on use case
    const strategy = request.strategy || 'quality';
    const isArtistic = request.style === 'artistic' || request.style === 'vibrant';
    const isProfessional = request.style === 'professional' || request.platform === 'linkedin';
    const needsSpeed = strategy === 'ultra_fast' || strategy === 'fast';

    // Fal.ai is better for artistic, creative content
    if (isArtistic && !needsSpeed) {
      return { primary: 'fal', fallback: 'openai' };
    }

    // OpenAI is better for professional, standard content and speed
    if (isProfessional || needsSpeed) {
      return { primary: 'openai', fallback: 'fal' };
    }

    // Default: try OpenAI first (more reliable), fallback to Fal
    return { primary: 'openai', fallback: 'fal' };
  }

  /**
   * Get optimal provider mix for variations
   */
  private getOptimalProviderMix(request: UnifiedImageRequest, count: number): ImageProvider[] {
    const providers: ImageProvider[] = [];
    const strategy = this.selectProvider(request);
    
    // Distribute across providers for variety
    for (let i = 0; i < count; i++) {
      if (i === 0) {
        providers.push(strategy.primary);
      } else if (i === 1 && strategy.fallback) {
        providers.push(strategy.fallback);
      } else {
        // Alternate between providers
        providers.push(i % 2 === 0 ? strategy.primary : (strategy.fallback || strategy.primary));
      }
    }
    
    return providers;
  }

  /**
   * Map unified size to OpenAI format
   */
  private mapSizeToOpenAI(
    size?: string,
    aspectRatio?: 'square' | 'landscape' | 'portrait'
  ): '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792' {
    if (size) {
      // Direct size mapping
      const sizeMap: Record<string, any> = {
        'small': '512x512',
        'medium': '1024x1024',
        'large': '1024x1024',
        'xl': '1792x1024',
        '256x256': '256x256',
        '512x512': '512x512',
        '1024x1024': '1024x1024',
        '1792x1024': '1792x1024',
        '1024x1792': '1024x1792',
      };
      
      if (sizeMap[size]) {
        return sizeMap[size];
      }
    }

    // Aspect ratio based mapping
    if (aspectRatio === 'landscape') {
      return '1792x1024';
    } else if (aspectRatio === 'portrait') {
      return '1024x1792';
    }
    
    return '1024x1024'; // Default square
  }

  /**
   * Map unified size to Fal format
   */
  private mapSizeToFal(
    size?: string,
    aspectRatio?: 'square' | 'landscape' | 'portrait'
  ): 'square_hd' | 'square' | 'portrait_4_3' | 'portrait_16_9' | 'landscape_4_3' | 'landscape_16_9' {
    if (aspectRatio === 'landscape') {
      return 'landscape_16_9';
    } else if (aspectRatio === 'portrait') {
      return 'portrait_4_3';
    }
    
    return 'square_hd'; // Default high-quality square
  }

  /**
   * Map unified style to OpenAI format
   */
  private mapStyleToOpenAI(style?: string): 'vivid' | 'natural' {
    const styleMap: Record<string, 'vivid' | 'natural'> = {
      'minimal': 'natural',
      'professional': 'natural',
      'vibrant': 'vivid',
      'artistic': 'vivid',
      'vivid': 'vivid',
      'natural': 'natural',
    };
    
    return styleMap[style || ''] || 'vivid';
  }

  /**
   * Get inference steps based on strategy
   */
  private getInferenceSteps(strategy: ModelStrategy): number {
    const stepsMap: Record<ModelStrategy, number> = {
      'ultra_fast': 20,
      'fast': 25,
      'quality': 35,
      'bulk': 25,
      'auto': 30,
    };
    
    return stepsMap[strategy] || 30;
  }

  /**
   * Get OpenAI pricing
   */
  private getOpenAICost(model: string, size: string): number {
    const pricing: Record<string, Record<string, number>> = {
      'dall-e-2': {
        '256x256': 0.016,
        '512x512': 0.018,
        '1024x1024': 0.020,
      },
      'dall-e-3': {
        '1024x1024': 0.040,
        '1792x1024': 0.080,
        '1024x1792': 0.080,
      },
    };
    
    return pricing[model]?.[size] || 0.040;
  }

  /**
   * Test all providers
   */
  async testAllProviders(): Promise<{
    openai: boolean;
    fal: boolean;
    overall: boolean;
  }> {
    const results = {
      openai: false,
      fal: false,
      overall: false,
    };

    // Test OpenAI
    try {
      const openaiClient = getOpenAIClient();
      results.openai = await openaiClient.testConnection();
    } catch (error) {
      console.error('OpenAI test failed:', error);
    }

    // Test Fal.ai
    try {
      const falClient = getFalClient();
      results.fal = await falClient.testConnection();
    } catch (error) {
      console.error('Fal.ai test failed:', error);
    }

    results.overall = results.openai || results.fal;
    return results;
  }
}

/**
 * Convenience functions for common use cases
 */
export async function generateImage(request: UnifiedImageRequest): Promise<UnifiedImageResponse> {
  const client = UnifiedImageClient.getInstance();
  return client.generateImage(request);
}

export async function generateSocialMediaImage(
  prompt: string,
  options: {
    platform?: 'twitter' | 'instagram' | 'linkedin';
    style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
    provider?: ImageProvider;
  } = {}
): Promise<UnifiedImageResponse> {
  return generateImage({
    prompt,
    platform: options.platform,
    style: options.style,
    provider: options.provider || 'auto',
    strategy: 'quality',
    aspectRatio: options.platform === 'twitter' ? 'landscape' : 'square',
  });
}

export async function generateImageVariations(
  prompt: string,
  count: number = 3,
  options: Partial<UnifiedImageRequest> = {}
): Promise<UnifiedImageResponse[]> {
  const client = UnifiedImageClient.getInstance();
  return client.generateImageVariations({ prompt, ...options }, count);
}

export async function testImageGeneration(): Promise<{
  openai: boolean;
  fal: boolean;
  overall: boolean;
}> {
  const client = UnifiedImageClient.getInstance();
  return client.testAllProviders();
}