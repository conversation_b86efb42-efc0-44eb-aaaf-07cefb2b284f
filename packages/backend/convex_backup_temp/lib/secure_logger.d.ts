/**
 * Secure Production Logger
 * 🔐 SECURITY: Prevents sensitive data leakage in production logs
 */
import { maskApi<PERSON>ey } from './production_config';
interface LogMeta {
    userId?: string;
    requestId?: string;
    operation?: string;
    duration?: number;
    [key: string]: any;
}
interface LogError {
    message: string;
    stack?: string;
    code?: string;
    statusCode?: number;
    [key: string]: any;
}
/**
 * Sanitizes objects by removing or masking sensitive data
 */
declare function sanitizeObject(obj: any, depth?: number): any;
/**
 * Sanitizes strings by masking potential sensitive data
 */
declare function sanitizeString(str: string): string;
/**
 * Sanitizes error objects for production logging
 */
declare function sanitizeError(error: any): LogError;
/**
 * Secure logger that prevents sensitive data leakage
 */
export declare const logger: {
    /**
     * Log informational messages
     */
    info: (message: string, meta?: LogMeta) => void;
    /**
     * Log warning messages
     */
    warn: (message: string, meta?: LogMeta) => void;
    /**
     * Log error messages with secure error handling
     */
    error: (message: string, error?: any, meta?: LogMeta) => void;
    /**
     * Log debug messages (only in development)
     */
    debug: (message: string, meta?: LogMeta) => void;
    /**
     * Log API request/response information
     */
    api: (operation: string, duration: number, meta?: LogMeta) => void;
    /**
     * Log authentication events
     */
    auth: (event: string, userId?: string, meta?: LogMeta) => void;
    /**
     * Log security events (always logged, even in production)
     */
    security: (event: string, meta?: LogMeta) => void;
};
/**
 * Performance monitoring wrapper
 */
export declare function withPerformanceLogging<T>(operation: string, fn: () => Promise<T>): Promise<T>;
/**
 * Export sanitization functions for use in other modules
 */
export declare const sanitization: {
    sanitizeObject: typeof sanitizeObject;
    sanitizeString: typeof sanitizeString;
    sanitizeError: typeof sanitizeError;
    maskApiKey: typeof maskApiKey;
};
export {};
