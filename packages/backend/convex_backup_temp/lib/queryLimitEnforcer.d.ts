/**
 * 🚀 QUERY LIMIT ENFORCEMENT SYSTEM
 *
 * Advanced query protection to prevent runaway bandwidth consumption
 * and enforce strict limits on all database operations
 */
import { GenericDatabaseReader } from "convex/server";
/**
 * Global query limits to prevent bandwidth disasters
 */
export declare const QUERY_LIMITS: {
    readonly ABSOLUTE_MAX: {
        readonly QUERY_TAKE_LIMIT: 2000;
        readonly COLLECT_PROHIBITION: true;
        readonly MAX_CONCURRENT_QUERIES: 10;
        readonly MAX_EXECUTION_TIME: 30000;
    };
    readonly DASHBOARD: {
        readonly MAX_ACCOUNTS: 50;
        readonly MAX_TWEETS_PER_ACCOUNT: 500;
        readonly MAX_MENTIONS_PER_ACCOUNT: 300;
        readonly MAX_RESPONSES: 200;
    };
    readonly MENTIONS: {
        readonly MAX_RECENT_MENTIONS: 100;
        readonly MAX_UNPROCESSED_MENTIONS: 200;
        readonly MAX_SEARCH_RESULTS: 50;
        readonly MAX_STATS_MENTIONS: 500;
    };
    readonly TWEETS: {
        readonly MAX_RECENT_TWEETS: 200;
        readonly MAX_ACCOUNT_TWEETS: 100;
        readonly MAX_SEARCH_RESULTS: 50;
        readonly MAX_STATS_TWEETS: 1000;
    };
    readonly ANALYTICS: {
        readonly MAX_EVENTS: 1000;
        readonly MAX_LOGS: 500;
        readonly MAX_TIME_RANGE_DAYS: 90;
    };
    readonly CACHE: {
        readonly MAX_CACHE_ENTRIES: 10000;
        readonly MAX_CLEANUP_BATCH: 1000;
        readonly MAX_STATS_ENTRIES: 5000;
    };
};
/**
 * Query limit enforcer with automatic protection
 */
export declare class QueryLimitEnforcer {
    private static activeQueries;
    private static queryStartTimes;
    /**
     * Create a protected query with automatic limits
     */
    static createLimitedQuery<T>(db: GenericDatabaseReader<any>, tableName: string, operationType: keyof typeof QUERY_LIMITS, operationId?: string): ProtectedQuery<T>;
    /**
     * Enforce absolute maximum take limit
     */
    static enforceTakeLimit(requestedLimit: number, operationType: keyof typeof QUERY_LIMITS): number;
    /**
     * Check if .collect() is prohibited
     */
    static isCollectProhibited(): boolean;
    /**
     * Track query execution for monitoring
     */
    static trackQueryStart(operationId: string): void;
    /**
     * Track query completion
     */
    static trackQueryEnd(operationId: string): number;
    /**
     * Get current query statistics
     */
    static getQueryStats(): {
        activeQueries: number;
        longestRunningQuery: number;
        totalTrackedOperations: number;
    };
}
/**
 * Protected query wrapper with automatic limit enforcement
 */
export declare class ProtectedQuery<T> {
    private db;
    private tableName;
    private operationType;
    private operationId;
    constructor(db: GenericDatabaseReader<any>, tableName: string, operationType: keyof typeof QUERY_LIMITS, operationId: string);
    /**
     * Create a protected query with index
     */
    withIndex<IndexName extends string>(indexName: IndexName, indexQuery?: (q: any) => any): ProtectedQueryWithIndex<T>;
    /**
     * Create a protected query without index
     */
    query(): ProtectedQueryBuilder<T>;
}
/**
 * Protected query with index support
 */
export declare class ProtectedQueryWithIndex<T> {
    private db;
    private tableName;
    private operationType;
    private operationId;
    private indexName;
    private indexQuery?;
    constructor(db: GenericDatabaseReader<any>, tableName: string, operationType: keyof typeof QUERY_LIMITS, operationId: string, indexName: string, indexQuery?: ((q: any) => any) | undefined);
    /**
     * Execute the indexed query with protection
     */
    execute(): ProtectedQueryBuilder<T>;
}
/**
 * Protected query builder with limit enforcement
 */
export declare class ProtectedQueryBuilder<T> {
    private query;
    private operationType;
    private operationId;
    constructor(query: any, operationType: keyof typeof QUERY_LIMITS, operationId: string);
    /**
     * Add filter with protection
     */
    filter(filterFn: (q: any) => any): ProtectedQueryBuilder<T>;
    /**
     * Add ordering with protection
     */
    order(direction: "asc" | "desc"): ProtectedQueryBuilder<T>;
    /**
     * Take with automatic limit enforcement
     */
    take(limit: number): Promise<T[]>;
    /**
     * First with protection
     */
    first(): Promise<T | null>;
    /**
     * Collect is prohibited - throws error
     */
    collect(): Promise<never>;
}
/**
 * Bandwidth guard for high-level operations
 */
export declare class BandwidthGuard {
    /**
     * Check if an operation should be allowed based on recent bandwidth usage
     */
    static shouldAllowOperation(db: GenericDatabaseReader<any>, operationType: string, estimatedBandwidth: number): Promise<{
        allowed: boolean;
        reason?: string;
        suggestedDelay?: number;
    }>;
    /**
     * Estimate bandwidth for common operations
     */
    static estimateOperationBandwidth(operationType: string, recordCount: number): number;
}
/**
 * Safe query helpers for common patterns
 */
export declare class SafeQueryHelpers {
    /**
     * Safe user dashboard query with all protections
     */
    static getUserDashboardData(db: GenericDatabaseReader<any>, userId: string, timeframe?: string): Promise<any>;
    /**
     * Safe mention search with all protections
     */
    static searchMentionsSafely(db: GenericDatabaseReader<any>, searchTerm: string, limit?: number): Promise<any>;
}
