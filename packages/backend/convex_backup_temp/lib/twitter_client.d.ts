/**
 * TweetIO Client for BuddyChip Pro
 *
 * This client provides a clean interface to interact with <PERSON><PERSON><PERSON> (twitterapi.io)
 * for fetching tweets, user data, mentions, and engagement metrics.
 */
import type { TwitterUser, TwitterTweet, ConvexContext, UserTweetsOptions, SearchMentionsOptions, SearchTweetsOptions, UserTweetsResponse, TweetResponse, SearchResponse } from "../types/twitter";
export declare class TweetIOClient {
    private baseUrl;
    private apiKey;
    private config;
    private rateLimiter;
    private monitor;
    constructor(apiKey: string, options?: {
        baseUrl?: string;
        enableMonitoring?: boolean;
        ctx?: ConvexContext;
    });
    /**
     * Check if we're rate limited for a specific endpoint
     */
    private isRateLimited;
    /**
     * Update rate limit information from response headers
     */
    private updateRateLimitInfo;
    /**
     * Check quota limits before making requests
     */
    private checkQuotaLimits;
    /**
     * Track API usage for monitoring
     */
    private trackUsage;
    /**
     * Apply exponential backoff for retries with enhanced recovery
     */
    private applyBackoff;
    /**
     * Make authenticated request to TwitterAPI.io with comprehensive monitoring
     */
    private makeRequest;
    /**
     * Enhanced error handling wrapper with user-friendly messages
     */
    private handleApiError;
    /**
     * Get user information by username with enhanced error handling
     */
    getUserByUsername(username: string): Promise<TwitterUser | null>;
    /**
     * PHASE 2: Get user information by user ID
     * This method fills gaps when TwitterAPI.io doesn't return complete author data
     */
    getUserById(userId: string): Promise<TwitterUser | null>;
    /**
     * PHASE 2: Batch lookup multiple users by their IDs
     * Efficient way to fetch missing author data for multiple mentions
     */
    getUsersByIds(userIds: string[]): Promise<TwitterUser[]>;
    /**
     * Get recent tweets from a user
     */
    getUserTweets(userId: string, options?: UserTweetsOptions): Promise<{
        tweets: TwitterTweet[];
        users: TwitterUser[];
    }>;
    /**
     * Get tweets by username (convenience method)
     */
    getTweetsByUsername(username: string, options?: UserTweetsOptions): Promise<UserTweetsResponse>;
    /**
     * Get a specific tweet by ID using correct TwitterAPI.io endpoint
     */
    getTweetById(tweetId: string): Promise<TweetResponse>;
    /**
     * Get a tweet from a Twitter URL
     */
    getTweetFromUrl(url: string): Promise<TweetResponse>;
    /**
     * Search for mentions of a username using TwitterAPI.io user mentions endpoint
     */
    searchMentions(username: string, options?: SearchMentionsOptions): Promise<{
        tweets: TwitterTweet[];
        users: TwitterUser[];
    }>;
    /**
     * Search for tweets with a custom query using TwitterAPI.io
     */
    searchTweets(query: string, options?: SearchTweetsOptions): Promise<SearchResponse>;
    /**
     * Get rate limit status for debugging
     */
    getRateLimitStatus(): Record<string, import("../types/twitter").RateLimitInfo>;
}
/**
 * Create a TwitterAPI.io client instance
 * For use in Convex functions - pass the API key directly from context
 */
export declare function createTweetIOClient(apiKey?: string, ctx?: ConvexContext): TweetIOClient;
/**
 * Legacy function name for backwards compatibility
 * @deprecated Use createTweetIOClient instead
 */
export declare function createTwitterClient(apiKey?: string): TweetIOClient;
export { normalizeTwitterTweet, calculateMentionPriority } from "./twitter_utils";
