import { mutation, action, query } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";

/**
 * Advanced Mention Caching System
 * Implements intelligent caching strategies for optimal performance
 */

// Define types that match the schema definition
export interface CachedMentionEntry {
  id: string;
  content: string;
  author: string;
  timestamp: number;
}

export interface CachedAnalytics {
  totalCount: number;
  unreadCount: number;
  highPriorityCount: number;
}

export interface CachedAccount {
  id: string;
  handle: string;
  isActive: boolean;
}

// Schema-compliant cache data structure
export interface SchemaCacheData {
  mentions?: CachedMentionEntry[];
  analytics?: CachedAnalytics;
  accounts?: CachedAccount[];
  searchResults?: string[];
}

export interface CacheEntry {
  key: string;
  data: SchemaCacheData;
  timestamp: number;
  ttl: number;
  hits: number;
  lastAccessed: number;
  tags: string[];
}

export interface CacheStats {
  totalEntries: number;
  totalHits: number;
  totalMisses: number;
  hitRate: number;
  memoryUsage: number;
  oldestEntry: number;
  newestEntry: number;
}

/**
 * Cache storage using Convex database
 * Optimized for mention-related data with intelligent expiration
 */
// Schema-compliant validators
const cachedMentionEntryValidator = v.object({
  id: v.string(),
  content: v.string(),
  author: v.string(),
  timestamp: v.number(),
});

const cachedAnalyticsValidator = v.object({
  totalCount: v.number(),
  unreadCount: v.number(),
  highPriorityCount: v.number(),
});

const cachedAccountValidator = v.object({
  id: v.string(),
  handle: v.string(),
  isActive: v.boolean(),
});

// Cache data validator that matches the schema exactly
const cacheDataValidator = v.object({
  mentions: v.optional(v.array(cachedMentionEntryValidator)),
  analytics: v.optional(cachedAnalyticsValidator),
  accounts: v.optional(v.array(cachedAccountValidator)),
  searchResults: v.optional(v.array(v.string())),
});

export const setCacheEntry = mutation({
  args: {
    key: v.string(),
    data: cacheDataValidator,
    ttl: v.optional(v.number()), // Time to live in milliseconds
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const ttl = args.ttl || 3600000; // Default 1 hour
    
    // Check if entry already exists
    const existing = await ctx.db
      .query("mentionCache")
      .filter(q => q.eq(q.field("key"), args.key))
      .first();
    
    if (existing) {
      // Update existing entry
      await ctx.db.patch(existing._id, {
        data: args.data,
        timestamp: now,
        ttl,
        lastAccessed: now,
        tags: args.tags || existing.tags,
      });
      return existing._id;
    } else {
      // Create new entry
      return await ctx.db.insert("mentionCache", {
        key: args.key,
        data: args.data,
        timestamp: now,
        ttl,
        hits: 0,
        lastAccessed: now,
        tags: args.tags || [],
      });
    }
  },
});

/**
 * Get cache entry with automatic hit tracking
 */
export const getCacheEntry = mutation({
  args: {
    key: v.string(),
  },
  handler: async (ctx, args) => {
    const entry = await ctx.db
      .query("mentionCache")
      .filter(q => q.eq(q.field("key"), args.key))
      .first();
    
    if (!entry) {
      return null;
    }
    
    const now = Date.now();
    
    // Check if entry has expired
    if (now - entry.timestamp > entry.ttl) {
      // Entry expired, clean it up
      await ctx.db.delete(entry._id);
      return null;
    }
    
    // Update hit count and last accessed time
    await ctx.db.patch(entry._id, {
      hits: entry.hits + 1,
      lastAccessed: now,
    });
    
    return {
      data: entry.data,
      timestamp: entry.timestamp,
      hits: entry.hits + 1,
      age: now - entry.timestamp,
    };
  },
});

/**
 * Invalidate cache entries by key pattern or tags
 */
export const invalidateCache = mutation({
  args: {
    keyPattern: v.optional(v.string()), // Regex pattern for keys
    tags: v.optional(v.array(v.string())), // Tags to invalidate
    olderThan: v.optional(v.number()), // Invalidate entries older than timestamp
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("mentionCache");
    
    const entries = await query.collect();
    const toDelete = [];
    
    for (const entry of entries) {
      let shouldDelete = false;
      
      // Check key pattern
      if (args.keyPattern) {
        try {
          const regex = new RegExp(args.keyPattern);
          if (regex.test(entry.key)) {
            shouldDelete = true;
          }
        } catch (error) {
          console.error("Invalid regex pattern:", args.keyPattern);
        }
      }
      
      // Check tags
      if (args.tags && !shouldDelete) {
        const hasMatchingTag = args.tags.some(tag => 
          entry.tags.includes(tag)
        );
        if (hasMatchingTag) {
          shouldDelete = true;
        }
      }
      
      // Check age
      if (args.olderThan && !shouldDelete) {
        if (entry.timestamp < args.olderThan) {
          shouldDelete = true;
        }
      }
      
      if (shouldDelete) {
        toDelete.push(entry._id);
      }
    }
    
    // Delete in batches
    for (const id of toDelete) {
      await ctx.db.delete(id);
    }
    
    return { deleted: toDelete.length };
  },
});

/**
 * Clean up expired cache entries
 */
export const cleanupExpiredCache = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const entries = await ctx.db.query("mentionCache").collect();
    
    const expired = entries.filter(entry => 
      now - entry.timestamp > entry.ttl
    );
    
    for (const entry of expired) {
      await ctx.db.delete(entry._id);
    }
    
    return { cleaned: expired.length };
  },
});

/**
 * Get cache statistics
 */
export const getCacheStats = query({
  args: {},
  handler: async (ctx) => {
    const entries = await ctx.db.query("mentionCache").collect();
    
    if (entries.length === 0) {
      return {
        totalEntries: 0,
        totalHits: 0,
        totalMisses: 0,
        hitRate: 0,
        memoryUsage: 0,
        oldestEntry: 0,
        newestEntry: 0,
      };
    }
    
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const timestamps = entries.map(entry => entry.timestamp);
    const memoryUsage = entries.reduce((sum, entry) => {
      return sum + JSON.stringify(entry.data).length;
    }, 0);
    
    return {
      totalEntries: entries.length,
      totalHits,
      totalMisses: 0, // Would need separate tracking
      hitRate: totalHits / Math.max(entries.length, 1),
      memoryUsage,
      oldestEntry: Math.min(...timestamps),
      newestEntry: Math.max(...timestamps),
    };
  },
});

/**
 * Smart cache warming for mentions
 */
export const warmMentionCache = action({
  args: {
    accountIds: v.optional(v.array(v.id("twitterAccounts"))),
    preloadHours: v.optional(v.number()),
  },
  handler: async (_ctx, _args) => {
    // TODO: Implement after creating required query functions
    // This function is disabled until the required API endpoints are implemented
    return {
      success: false,
      error: "Function disabled - requires query function implementation",
      accountsProcessed: 0,
      entriesWarmed: 0,
    };
  },
});

/**
 * Intelligent cache-first mention queries
 */
export const getCachedMentionStats = query({
  args: {
    accountId: v.optional(v.id("twitterAccounts")),
    maxAge: v.optional(v.number()), // Max age in ms
  },
  handler: async (ctx, args) => {
    const maxAge = args.maxAge || 300000; // 5 minutes default
    const cacheKey = args.accountId ? 
      `mention_stats_${args.accountId}` : 
      "mention_stats_global";
    
    // Try cache first (read-only in query context)
    const cacheEntry = await ctx.db
      .query("mentionCache")
      .filter(q => q.eq(q.field("key"), cacheKey))
      .first();
    
    const now = Date.now();
    
    if (cacheEntry && 
        now - cacheEntry.timestamp < maxAge && 
        cacheEntry.data.analytics) {
      return {
        total: cacheEntry.data.analytics.totalCount,
        unread: cacheEntry.data.analytics.unreadCount,
        highPriority: cacheEntry.data.analytics.highPriorityCount,
        processed: 0, // Not stored in cache analytics
        responseOpportunities: 0, // Not stored in cache analytics
        lastUpdated: cacheEntry.timestamp,
        cached: true,
        cacheAge: now - cacheEntry.timestamp,
        cacheHits: cacheEntry.hits,
      };
    }
    
    // Cache miss - compute fresh data directly from mentions table
    const mentions = args.accountId 
      ? await ctx.db
          .query("mentions")
          .withIndex("by_monitored_account", q => 
            q.eq("monitoredAccountId", args.accountId!)
          )
          .collect()
      : await ctx.db.query("mentions").collect();
    
    const stats = {
      total: mentions.length,
      unread: mentions.filter(m => !m.isNotificationSent).length,
      highPriority: mentions.filter(m => m.priority === "high").length,
      processed: mentions.filter(m => m.isProcessed).length,
      responseOpportunities: mentions.filter(m => 
        m.aiAnalysisResult?.shouldRespond && !m.isProcessed
      ).length,
      lastUpdated: now,
    };
    
    // Note: Cache update should happen via a separate mutation call
    // In query context, we can only read data
    
    return {
      ...stats,
      cached: false,
      cacheAge: 0,
      cacheHits: 0,
    };
  },
});

/**
 * Update mention stats cache with fresh data
 */
export const updateMentionStatsCache = mutation({
  args: {
    accountId: v.optional(v.id("twitterAccounts")),
    maxAge: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const maxAge = args.maxAge || 300000; // 5 minutes default
    const cacheKey = args.accountId ? 
      `mention_stats_${args.accountId}` : 
      "mention_stats_global";
    
    // Compute fresh data
    const mentions = args.accountId 
      ? await ctx.db
          .query("mentions")
          .withIndex("by_monitored_account", q => 
            q.eq("monitoredAccountId", args.accountId!)
          )
          .collect()
      : await ctx.db.query("mentions").collect();
    
    // Update cache with schema-compliant data
    await ctx.runMutation(api.lib.mentionCache.setCacheEntry, {
      key: cacheKey,
      data: {
        analytics: {
          totalCount: mentions.length,
          unreadCount: mentions.filter(m => !m.isNotificationSent).length,
          highPriorityCount: mentions.filter(m => m.priority === "high").length,
        }
      },
      ttl: maxAge,
      tags: ["stats", args.accountId ? `account_${args.accountId}` : "global"],
    });
    
    return {
      success: true,
      cacheKey,
      updatedAt: Date.now(),
    };
  },
});

/**
 * Batch cache operations for performance
 */
export const batchCacheOperations = mutation({
  args: {
    operations: v.array(v.object({
      type: v.union(v.literal("set"), v.literal("get"), v.literal("delete")),
      key: v.string(),
      data: v.optional(cacheDataValidator),
      ttl: v.optional(v.number()),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const results = [];
    
    for (const op of args.operations) {
      try {
        switch (op.type) {
          case "set":
            const setResult = await ctx.runMutation(api.lib.mentionCache.setCacheEntry, {
              key: op.key,
              data: op.data,
              ttl: op.ttl,
              tags: op.tags,
            });
            results.push({ key: op.key, success: true, result: setResult });
            break;
            
          case "get":
            const getResult = await ctx.runQuery(api.lib.mentionCache.getCacheEntry, {
              key: op.key,
            });
            results.push({ key: op.key, success: true, result: getResult });
            break;
            
          case "delete":
            const entry = await ctx.db
              .query("mentionCache")
              .filter(q => q.eq(q.field("key"), op.key))
              .first();
            if (entry) {
              await ctx.db.delete(entry._id);
              results.push({ key: op.key, success: true, result: "deleted" });
            } else {
              results.push({ key: op.key, success: false, error: "not found" });
            }
            break;
        }
      } catch (error) {
        results.push({ 
          key: op.key, 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }
    
    return {
      totalOperations: args.operations.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  },
});
