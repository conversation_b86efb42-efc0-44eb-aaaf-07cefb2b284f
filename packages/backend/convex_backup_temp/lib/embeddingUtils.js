/**
 * Embedding utilities for vector operations and semantic analysis
 */
/**
 * Calculate cosine similarity between two vectors
 */
export function calculateCosineSimilarity(vectorA, vectorB) {
    if (vectorA.length !== vectorB.length) {
        throw new Error('Vectors must have the same length');
    }
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    for (let i = 0; i < vectorA.length; i++) {
        dotProduct += vectorA[i] * vectorB[i];
        normA += vectorA[i] * vectorA[i];
        normB += vectorB[i] * vectorB[i];
    }
    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);
    if (normA === 0 || normB === 0) {
        return 0;
    }
    return dotProduct / (normA * normB);
}
/**
 * Average multiple embeddings into a single vector
 */
export function averageEmbeddings(embeddings) {
    if (embeddings.length === 0) {
        return [];
    }
    const length = embeddings[0].length;
    const result = new Array(length).fill(0);
    for (const embedding of embeddings) {
        if (embedding.length !== length) {
            throw new Error('All embeddings must have the same length');
        }
        for (let i = 0; i < length; i++) {
            result[i] += embedding[i];
        }
    }
    for (let i = 0; i < length; i++) {
        result[i] /= embeddings.length;
    }
    return result;
}
/**
 * Preprocess text for embedding generation
 */
export function preprocessTextForEmbedding(text) {
    return text
        .replace(/@\w+/g, '') // Remove mentions
        .replace(/#\w+/g, '') // Remove hashtags
        .replace(/https?:\/\/[^\s]+/g, '') // Remove URLs
        .replace(/[^\w\s]/g, ' ') // Remove special characters
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim()
        .toLowerCase();
}
/**
 * Calculate enhanced worthiness score based on multiple factors
 */
export function calculateEnhancedWorthinessScore(params) {
    let score = 0;
    // Content quality (40% weight)
    const contentLength = params.content.length;
    const hasQuestion = params.content.includes('?');
    const hasLink = /https?:\/\//.test(params.content);
    const hasHashtag = /#\w+/.test(params.content);
    let contentScore = 0;
    if (contentLength > 50 && contentLength < 200)
        contentScore += 0.3;
    if (hasQuestion)
        contentScore += 0.2;
    if (hasLink)
        contentScore += 0.1;
    if (hasHashtag)
        contentScore += 0.05;
    score += contentScore * 0.4;
    // Author authority (25% weight)
    let authorScore = 0;
    if (params.authorIsVerified)
        authorScore += 0.3;
    if (params.authorFollowerCount) {
        const followerScore = Math.min(params.authorFollowerCount / 100000, 1) * 0.2;
        authorScore += followerScore;
    }
    score += authorScore * 0.25;
    // Engagement (20% weight)
    let engagementScore = 0;
    if (params.engagement) {
        const totalEngagement = params.engagement.likes +
            params.engagement.retweets +
            params.engagement.replies;
        engagementScore = Math.min(totalEngagement / 100, 1);
    }
    score += engagementScore * 0.2;
    // Semantic relevance (10% weight)
    if (params.semanticScore !== undefined) {
        score += params.semanticScore * 0.1;
    }
    // Recency bonus (5% weight)
    if (params.recency !== undefined) {
        const recencyScore = Math.max(0, 1 - (params.recency / 24)); // Decay over 24 hours
        score += recencyScore * 0.05;
    }
    return Math.min(Math.max(score, 0), 1); // Clamp between 0 and 1
}
/**
 * Calculate batch insights from tweet analysis results
 */
export function calculateBatchInsights(analyses) {
    if (analyses.length === 0) {
        return {
            averageWorthiness: 0,
            topTopics: [],
            engagementPatterns: {},
            recommendedActions: []
        };
    }
    // Calculate average worthiness
    const averageWorthiness = analyses.reduce((sum, analysis) => {
        return sum + (analysis.worthinessScore || 0);
    }, 0) / analyses.length;
    // Extract topics from analyses
    const allTopics = analyses.flatMap(analysis => analysis.topics || []);
    const topicCounts = allTopics.reduce((counts, topic) => {
        counts[topic] = (counts[topic] || 0) + 1;
        return counts;
    }, {});
    const topTopics = Object.entries(topicCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([topic]) => topic);
    // Calculate engagement patterns
    const engagementPatterns = {
        averageEngagement: analyses.reduce((sum, analysis) => {
            const engagement = analysis.engagement || { likes: 0, retweets: 0, replies: 0 };
            return sum + engagement.likes + engagement.retweets + engagement.replies;
        }, 0) / analyses.length,
        highEngagementCount: analyses.filter(analysis => (analysis.engagement?.likes || 0) > 10 ||
            (analysis.engagement?.retweets || 0) > 5).length
    };
    // Generate recommended actions
    const recommendedActions = [];
    if (averageWorthiness > 0.7) {
        recommendedActions.push("High-quality content detected - consider engaging");
    }
    if (topTopics.length > 0) {
        recommendedActions.push(`Focus on trending topics: ${topTopics.slice(0, 3).join(', ')}`);
    }
    if (engagementPatterns.highEngagementCount > analyses.length * 0.3) {
        recommendedActions.push("Multiple high-engagement opportunities identified");
    }
    return {
        averageWorthiness,
        topTopics,
        engagementPatterns,
        recommendedActions
    };
}
