/**
 * API monitoring and usage tracking for Twitter integration
 */
import type { ConvexContext, TwitterClientConfig } from "../types/twitter";
export declare class TwitterAPIMonitor {
    private config;
    private ctx?;
    private enableMonitoring;
    constructor(config: TwitterClientConfig, ctx?: ConvexContext | undefined, enableMonitoring?: boolean);
    /**
     * Check quota limits before making requests
     */
    checkQuotaLimits(): Promise<boolean>;
    /**
     * Track API usage for monitoring
     */
    trackUsage(endpoint: string, startTime: number, response: Response, requestCount?: number, error?: Error): Promise<void>;
    /**
     * Estimate the cost of an API call
     */
    private estimateCost;
    /**
     * Get current usage statistics
     */
    getUsageStats(timeRange?: "hour" | "day" | "week" | "month"): Promise<any>;
    /**
     * Get quota status
     */
    getQuotaStatus(): Promise<any>;
    /**
     * Get cost analysis
     */
    getCostAnalysis(timeRange?: "day" | "week" | "month"): Promise<any>;
    /**
     * Run health check
     */
    runHealthCheck(): Promise<any>;
    /**
     * Log performance metrics
     */
    logPerformanceMetrics(endpoint: string, duration: number, requestSize?: number, responseSize?: number): Promise<void>;
    /**
     * Track error patterns for analysis
     */
    trackError(endpoint: string, errorType: string, errorMessage: string, statusCode?: number): Promise<void>;
    /**
     * Get error analytics
     */
    getErrorAnalytics(timeRange?: "hour" | "day" | "week"): Promise<any>;
}
