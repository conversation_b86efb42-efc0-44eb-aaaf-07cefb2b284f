/**
 * Utility functions for Twitter API integration
 */
/**
 * Extract Twitter username from various URL formats
 */
export function extractUsernameFromUrl(url) {
    const patterns = [
        /twitter\.com\/([^\/\?]+)/i,
        /x\.com\/([^\/\?]+)/i,
    ];
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1] && match[1] !== "i" && match[1] !== "home") {
            return match[1];
        }
    }
    return null;
}
/**
 * Extract tweet ID from Twitter URL
 */
export function extractTweetIdFromUrl(url) {
    const patterns = [
        /twitter\.com\/[^\/]+\/status\/(\d+)/i,
        /x\.com\/[^\/]+\/status\/(\d+)/i,
    ];
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            return match[1];
        }
    }
    return null;
}
/**
 * Validate Twitter URL format
 */
export function isValidTwitterUrl(url) {
    const patterns = [
        /^https?:\/\/(www\.)?(twitter|x)\.com\/[^\/\?]+/i,
        /^https?:\/\/(www\.)?(twitter|x)\.com\/[^\/]+\/status\/\d+/i,
    ];
    return patterns.some(pattern => pattern.test(url));
}
/**
 * Clean username by removing @ prefix
 */
export function cleanUsername(username) {
    return username.replace(/^@/, "");
}
/**
 * Add @ prefix to username if not present
 */
export function addAtPrefix(username) {
    return username.startsWith("@") ? username : `@${username}`;
}
/**
 * Normalize Twitter data for database storage
 */
export function normalizeTwitterTweet(tweet, users) {
    const author = users.find(u => u.id === tweet.author_id);
    return {
        tweetId: tweet.id,
        content: tweet.text,
        author: author?.name || "Unknown",
        authorHandle: author?.username || "unknown",
        authorProfileImage: author?.profile_image_url,
        isRetweet: tweet.referenced_tweets?.some(ref => ref.type === "retweeted") || false,
        retweetedFrom: tweet.referenced_tweets?.find(ref => ref.type === "retweeted")?.id,
        createdAt: new Date(tweet.created_at).getTime(),
        scrapedAt: Date.now(),
        engagement: {
            likes: tweet.public_metrics?.like_count || 0,
            retweets: tweet.public_metrics?.retweet_count || 0,
            replies: tweet.public_metrics?.reply_count || 0,
            views: tweet.public_metrics?.impression_count || 0,
        },
        url: `https://twitter.com/${author?.username || "unknown"}/status/${tweet.id}`,
        metadata: {
            isThread: tweet.conversation_id !== tweet.id,
            hasMedia: Boolean(tweet.attachments?.media_keys?.length),
            language: tweet.entities?.hashtags?.length ? "detected" : undefined,
        }
    };
}
/**
 * Calculate mention priority based on user metrics
 */
export function calculateMentionPriority(user) {
    const followers = user.followers_count || 0;
    const isVerified = user.verified || false;
    if (isVerified || followers > 10000) {
        return "high";
    }
    else if (followers > 1000) {
        return "medium";
    }
    else {
        return "low";
    }
}
/**
 * Convert TwitterIO tweet format to standard format
 */
export function convertTwitterIOTweet(tweet) {
    return {
        id: tweet.id,
        text: tweet.text,
        author_id: tweet.author?.id || "",
        created_at: tweet.createdAt,
        conversation_id: tweet.conversationId,
        in_reply_to_user_id: tweet.inReplyToUserId,
        referenced_tweets: tweet.referencedTweets || [],
        public_metrics: {
            like_count: tweet.metrics?.likeCount || tweet.likeCount || 0,
            retweet_count: tweet.metrics?.retweetCount || tweet.retweetCount || 0,
            reply_count: tweet.metrics?.replyCount || tweet.replyCount || 0,
            quote_count: tweet.metrics?.quoteCount || tweet.quoteCount || 0,
            impression_count: tweet.metrics?.viewCount || tweet.viewCount || 0,
        },
        entities: tweet.entities || {},
        context_annotations: tweet.contextAnnotations || []
    };
}
/**
 * Convert TwitterIO author to standard user format
 */
export function convertTwitterIOUser(author) {
    if (!author)
        return null;
    return {
        id: author.id,
        name: author.name,
        username: author.username,
        profile_image_url: author.profileImageUrl,
        verified: author.verified || false,
        followers_count: author.followersCount || 0,
        following_count: author.followingCount || 0,
        tweet_count: author.tweetCount || 0,
        description: author.description,
        location: author.location,
        created_at: author.createdAt,
        public_metrics: {
            followers_count: author.followersCount || 0,
            following_count: author.followingCount || 0,
            tweet_count: author.tweetCount || 0,
            listed_count: 0
        }
    };
}
/**
 * Build Twitter reply URL with pre-filled text
 */
export function buildTwitterReplyUrl(tweetId, replyText) {
    return `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodeURIComponent(replyText)}`;
}
/**
 * Build Twitter URL from username and tweet ID
 */
export function buildTwitterUrl(username, tweetId) {
    const cleanedUsername = cleanUsername(username);
    return `https://twitter.com/${cleanedUsername}/status/${tweetId}`;
}
/**
 * Extract hashtags from tweet text
 */
export function extractHashtags(text) {
    const hashtagPattern = /#[\w]+/g;
    const matches = text.match(hashtagPattern);
    return matches ? matches.map(tag => tag.substring(1)) : [];
}
/**
 * Extract mentions from tweet text
 */
export function extractMentions(text) {
    const mentionPattern = /@[\w]+/g;
    const matches = text.match(mentionPattern);
    return matches ? matches.map(mention => mention.substring(1)) : [];
}
/**
 * Extract URLs from tweet text
 */
export function extractUrls(text) {
    const urlPattern = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g;
    const matches = text.match(urlPattern);
    return matches || [];
}
/**
 * Calculate engagement rate for a tweet
 */
export function calculateEngagementRate(tweet) {
    const metrics = tweet.public_metrics;
    if (!metrics || !metrics.impression_count || metrics.impression_count === 0) {
        return 0;
    }
    const totalEngagement = (metrics.like_count || 0) +
        (metrics.retweet_count || 0) +
        (metrics.reply_count || 0) +
        (metrics.quote_count || 0);
    return (totalEngagement / metrics.impression_count) * 100;
}
/**
 * Determine if a tweet is potentially viral
 */
export function isViralTweet(tweet) {
    const metrics = tweet.public_metrics;
    if (!metrics)
        return false;
    const likes = metrics.like_count || 0;
    const retweets = metrics.retweet_count || 0;
    const replies = metrics.reply_count || 0;
    // Simple viral detection heuristics
    return likes > 1000 || retweets > 500 || replies > 200;
}
/**
 * Format large numbers for display (e.g., 1.2K, 3.4M)
 */
export function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + "M";
    }
    else if (num >= 1000) {
        return (num / 1000).toFixed(1) + "K";
    }
    else {
        return num.toString();
    }
}
/**
 * Format timestamp for display
 */
export function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    if (minutes < 60) {
        return `${minutes}m`;
    }
    else if (hours < 24) {
        return `${hours}h`;
    }
    else if (days < 7) {
        return `${days}d`;
    }
    else {
        return date.toLocaleDateString();
    }
}
/**
 * Remove duplicates from array of Twitter users
 */
export function deduplicateUsers(users) {
    const seen = new Set();
    return users.filter(user => {
        if (seen.has(user.id)) {
            return false;
        }
        seen.add(user.id);
        return true;
    });
}
/**
 * Remove duplicates from array of Twitter tweets
 */
export function deduplicateTweets(tweets) {
    const seen = new Set();
    return tweets.filter(tweet => {
        if (seen.has(tweet.id)) {
            return false;
        }
        seen.add(tweet.id);
        return true;
    });
}
/**
 * PHASE 1: USERNAME FALLBACK UTILITIES
 * These functions provide comprehensive fallback strategies for missing username data
 */
/**
 * Extract username from tweet text mentions
 * Looks for @username patterns in the tweet content
 */
export function extractUsernameFromTweetText(tweetText, authorId) {
    const mentionPattern = /@([a-zA-Z0-9_]+)/g;
    const matches = tweetText.match(mentionPattern);
    if (!matches)
        return null;
    // Return the first mention that's not a reply (usually the author)
    for (const match of matches) {
        const username = match.substring(1); // Remove @
        if (username.length > 0) {
            return username;
        }
    }
    return null;
}
/**
 * Generate fallback username from user ID
 * Creates a valid username when no other data is available
 */
export function generateFallbackUsername(userId, userDisplayName) {
    // Try to create username from display name first
    if (userDisplayName) {
        const cleanName = userDisplayName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '') // Remove special characters
            .substring(0, 15); // Limit length
        if (cleanName.length >= 3) {
            return `${cleanName}_${userId.substring(0, 4)}`;
        }
    }
    // Fallback to user ID pattern
    return `user_${userId.substring(0, 8)}`;
}
/**
 * Extract username from tweet entities (mentions)
 * Looks for username in tweet entities.mentions array
 */
export function extractUsernameFromEntities(tweet) {
    if (!tweet.entities?.mentions)
        return null;
    // Find the mention that corresponds to the tweet author
    const authorMention = tweet.entities.mentions.find(mention => mention.id === tweet.author_id);
    return authorMention?.username || null;
}
/**
 * Comprehensive username resolution with multiple fallback strategies
 * This is the main function to use when username is missing
 */
export function resolveUsername(options) {
    const { user, tweet, authorId, displayName } = options;
    // Strategy 1: Use existing username if available
    if (user?.username) {
        return cleanUsername(user.username);
    }
    // Strategy 2: Extract from tweet entities
    if (tweet) {
        const entityUsername = extractUsernameFromEntities(tweet);
        if (entityUsername) {
            return cleanUsername(entityUsername);
        }
    }
    // Strategy 3: Extract from tweet text
    if (tweet) {
        const textUsername = extractUsernameFromTweetText(tweet.text, authorId);
        if (textUsername) {
            return cleanUsername(textUsername);
        }
    }
    // Strategy 4: Generate fallback from display name or ID
    return generateFallbackUsername(authorId, displayName || user?.name);
}
/**
 * Validate and sanitize mention data before storage
 * Ensures all required fields are present and valid
 */
export function sanitizeMentionData(mentionData) {
    const warnings = [];
    const sanitized = { ...mentionData };
    // Ensure mentionAuthorHandle exists
    if (!sanitized.mentionAuthorHandle) {
        if (sanitized.mentionAuthor && sanitized.mentionTweetId) {
            // Try to resolve username using available data
            sanitized.mentionAuthorHandle = resolveUsername({
                authorId: sanitized.mentionTweetId.substring(0, 8), // Use part of tweet ID as fallback
                displayName: sanitized.mentionAuthor
            });
            warnings.push('Generated fallback username from display name');
        }
        else {
            sanitized.mentionAuthorHandle = `unknown_${Date.now()}`;
            warnings.push('Used timestamp-based fallback username');
        }
    }
    // Ensure URL is properly formatted
    if (sanitized.url && sanitized.url.includes('undefined')) {
        sanitized.url = `https://twitter.com/${sanitized.mentionAuthorHandle}/status/${sanitized.mentionTweetId}`;
        warnings.push('Fixed malformed URL with undefined username');
    }
    // Ensure all required fields have valid values
    const requiredFields = [
        'mentionTweetId', 'mentionContent', 'mentionAuthor',
        'mentionAuthorHandle', 'monitoredAccountId', 'mentionType',
        'engagement', 'priority', 'createdAt'
    ];
    let isValid = true;
    for (const field of requiredFields) {
        if (sanitized[field] === undefined || sanitized[field] === null) {
            isValid = false;
            warnings.push(`Missing required field: ${field}`);
        }
    }
    return {
        isValid,
        sanitized,
        warnings
    };
}
/**
 * Enhanced convertTwitterIOUser with fallback username resolution
 */
export function convertTwitterIOUserWithFallback(author, tweet) {
    if (!author)
        return null;
    // Use the comprehensive username resolution
    const username = resolveUsername({
        user: { ...author, username: author.username },
        tweet,
        authorId: author.id,
        displayName: author.name
    });
    return {
        id: author.id,
        name: author.name,
        username, // Always guaranteed to have a value
        profile_image_url: author.profileImageUrl,
        verified: author.verified || false,
        followers_count: author.followersCount || 0,
        following_count: author.followingCount || 0,
        tweet_count: author.tweetCount || 0,
        description: author.description,
        location: author.location,
        created_at: author.createdAt,
        public_metrics: {
            followers_count: author.followersCount || 0,
            following_count: author.followingCount || 0,
            tweet_count: author.tweetCount || 0,
            listed_count: 0
        }
    };
}
/**
 * PHASE 2: Enhanced author resolution with API lookup fallback
 * When author data is missing, attempts to fetch it from TwitterAPI.io
 */
export async function resolveAuthorWithApiLookup(options) {
    const { authorId, existingAuthor, tweet, twitterClient } = options;
    // Step 1: If we have complete author data, use it
    if (existingAuthor?.username && existingAuthor?.name) {
        return {
            author: existingAuthor,
            username: cleanUsername(existingAuthor.username),
            name: existingAuthor.name,
            wasResolved: false
        };
    }
    // Step 2: Try to fetch user data by ID using TwitterAPI.io
    if (twitterClient && typeof twitterClient.getUserById === 'function') {
        try {
            console.log(`🔍 Attempting API lookup for author ID: ${authorId}`);
            const fetchedAuthor = await twitterClient.getUserById(authorId);
            if (fetchedAuthor?.username) {
                console.log(`✅ Successfully resolved author: @${fetchedAuthor.username}`);
                return {
                    author: fetchedAuthor,
                    username: cleanUsername(fetchedAuthor.username),
                    name: fetchedAuthor.name,
                    wasResolved: true
                };
            }
        }
        catch (error) {
            console.warn(`⚠️ API lookup failed for author ${authorId}:`, error);
        }
    }
    // Step 3: Fall back to Phase 1 username resolution
    const fallbackUsername = resolveUsername({
        user: existingAuthor,
        tweet,
        authorId,
        displayName: existingAuthor?.name
    });
    const fallbackName = existingAuthor?.name || `Unknown User (${authorId.substring(0, 8)})`;
    // Create fallback author object
    const fallbackAuthor = {
        id: authorId,
        name: fallbackName,
        username: fallbackUsername,
        followers_count: existingAuthor?.followers_count || 0,
        verified: existingAuthor?.verified || false,
        following_count: existingAuthor?.following_count || 0,
        tweet_count: existingAuthor?.tweet_count || 0,
        public_metrics: {
            followers_count: existingAuthor?.followers_count || 0,
            following_count: existingAuthor?.following_count || 0,
            tweet_count: existingAuthor?.tweet_count || 0,
            listed_count: 0
        }
    };
    console.log(`🔄 Using fallback resolution for author ${authorId}: @${fallbackUsername}`);
    return {
        author: fallbackAuthor,
        username: fallbackUsername,
        name: fallbackName,
        wasResolved: false
    };
}
/**
 * PHASE 2: Batch resolve multiple missing authors
 * Efficiently resolves multiple missing authors in a single operation
 */
export async function batchResolveAuthors(options) {
    const { missingAuthorIds, twitterClient, maxBatchSize = 10 } = options;
    const resolvedAuthors = new Map();
    if (!twitterClient || missingAuthorIds.length === 0) {
        return resolvedAuthors;
    }
    console.log(`🔍 Batch resolving ${missingAuthorIds.length} missing authors`);
    // Process in batches to respect rate limits
    for (let i = 0; i < missingAuthorIds.length; i += maxBatchSize) {
        const batch = missingAuthorIds.slice(i, i + maxBatchSize);
        try {
            // Use batch lookup if available, otherwise individual lookups
            if (typeof twitterClient.getUsersByIds === 'function') {
                const users = await twitterClient.getUsersByIds(batch);
                users.forEach((user) => {
                    if (user.username) {
                        resolvedAuthors.set(user.id, user);
                    }
                });
            }
            else {
                // Fall back to individual lookups
                for (const authorId of batch) {
                    try {
                        const user = await twitterClient.getUserById(authorId);
                        if (user?.username) {
                            resolvedAuthors.set(authorId, user);
                        }
                    }
                    catch (error) {
                        console.warn(`Failed to resolve author ${authorId}:`, error);
                    }
                    // Rate limiting delay
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
        }
        catch (error) {
            console.error(`Batch resolve error for batch starting at index ${i}:`, error);
        }
        // Delay between batches
        if (i + maxBatchSize < missingAuthorIds.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    console.log(`✅ Successfully resolved ${resolvedAuthors.size}/${missingAuthorIds.length} authors`);
    return resolvedAuthors;
}
