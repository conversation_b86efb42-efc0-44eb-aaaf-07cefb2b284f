import OpenAI from 'openai';

/**
 * OpenAI Client Configuration for Direct OpenAI API Access
 * Used specifically for image generation with gpt-image-1 model
 */

export interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  maxRetries?: number;
  timeout?: number;
}

export interface ImageGenerationResponse {
  url?: string;
  base64?: string;
  revisedPrompt?: string;
  model: string;
  usage?: {
    totalTokens: number;
  };
}

export interface ResponsesAPIImageResponse {
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    image?: {
      url?: string;
      base64?: string;
      detail?: string;
    };
  }>;
  model: string;
  usage?: {
    totalTokens: number;
  };
}

/**
 * OpenAI client for image generation and Responses API
 */
export class OpenAIClient {
  private client: OpenAI;
  private config: Required<OpenAIConfig>;

  constructor(config: OpenAIConfig) {
    this.config = {
      baseURL: 'https://api.openai.com/v1',
      maxRetries: 3,
      timeout: 60000,
      ...config,
    };

    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL,
      maxRetries: this.config.maxRetries,
      timeout: this.config.timeout,
    });
  }

  /**
   * Generate image using DALL-E 3 model
   */
  async generateImage(
    prompt: string,
    options: {
      model?: 'dall-e-2' | 'dall-e-3';
      size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
      quality?: 'standard' | 'hd';
      style?: 'vivid' | 'natural';
      responseFormat?: 'url' | 'b64_json';
      user?: string;
    } = {}
  ): Promise<ImageGenerationResponse> {
    try {
      const defaultModel = (process.env.OPENAI_DEFAULT_IMAGE_MODEL || 'dall-e-3') as 'dall-e-2' | 'dall-e-3';
      const response = await this.client.images.generate({
        model: options.model || defaultModel,
        prompt,
        n: 1,
        size: options.size || '1024x1024',
        quality: options.quality || 'standard',
        style: options.style || 'vivid',
        response_format: options.responseFormat || 'url',
        user: options.user,
      });

      const imageData = response.data[0];
      if (!imageData) {
        throw new Error('No image data returned from OpenAI');
      }

      return {
        url: imageData.url,
        base64: imageData.b64_json,
        revisedPrompt: imageData.revised_prompt,
        model: defaultModel,
        usage: {
          totalTokens: 1, // DALL-E doesn't return token usage, placeholder
        },
      };
    } catch (error) {
      console.error('Image generation failed:', error);
      throw new Error(`Image generation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Generate response with image using OpenAI Responses API
   * Uses the image_generation tool to create both text and images
   */
  async generateResponseWithImage(
    prompt: string,
    options: {
      systemPrompt?: string;
      maxTokens?: number;
      temperature?: number;
      imagePrompt?: string;
      imageModel?: 'gpt-image-1';
      imageSize?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
      imageQuality?: 'standard' | 'hd';
      imageStyle?: 'vivid' | 'natural';
    } = {}
  ): Promise<ResponsesAPIImageResponse> {
    try {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [];
      
      if (options.systemPrompt) {
        messages.push({
          role: 'system',
          content: options.systemPrompt,
        });
      }
      
      messages.push({
        role: 'user',
        content: prompt,
      });

      // Use the Responses API with image generation tool
      const response = await this.client.chat.completions.create({
        model: options.imageModel || process.env.OPENAI_RESPONSES_MODEL || 'gpt-image-1',
        messages,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature ?? 0.7,
        tools: [
          {
            type: 'function',
            function: {
              name: 'image_generation',
              description: 'Generate an image to accompany the response',
              parameters: {
                type: 'object',
                properties: {
                  prompt: {
                    type: 'string',
                    description: 'The prompt for image generation',
                  },
                  size: {
                    type: 'string',
                    enum: ['256x256', '512x512', '1024x1024', '1792x1024', '1024x1792'],
                    description: 'Size of the generated image',
                  },
                  quality: {
                    type: 'string',
                    enum: ['standard', 'hd'],
                    description: 'Quality of the generated image',
                  },
                  style: {
                    type: 'string',
                    enum: ['vivid', 'natural'],
                    description: 'Style of the generated image',
                  },
                },
                required: ['prompt'],
              },
            },
          },
        ],
        tool_choice: options.imagePrompt ? 'required' : 'auto',
      });

      const choice = response.choices[0];
      if (!choice || !choice.message) {
        throw new Error('No valid response from OpenAI Responses API');
      }

      const content: ResponsesAPIImageResponse['content'] = [];

      // Add text content if present
      if (choice.message.content) {
        content.push({
          type: 'text',
          text: choice.message.content,
        });
      }

      // Process tool calls for image generation
      if (choice.message.tool_calls) {
        for (const toolCall of choice.message.tool_calls) {
          if (toolCall.function.name === 'image_generation') {
            try {
              const imageParams = JSON.parse(toolCall.function.arguments);
              const imageResponse = await this.generateImage(imageParams.prompt, {
                size: imageParams.size || options.imageSize,
                quality: imageParams.quality || options.imageQuality,
                style: imageParams.style || options.imageStyle,
                responseFormat: 'url',
              });

              content.push({
                type: 'image',
                image: {
                  url: imageResponse.url,
                  base64: imageResponse.base64,
                },
              });
            } catch (imageError) {
              console.error('Image generation in tool call failed:', imageError);
              // Continue without image rather than failing entirely
            }
          }
        }
      }

      return {
        content,
        model: response.model,
        usage: response.usage ? {
          totalTokens: response.usage.total_tokens,
        } : undefined,
      };
    } catch (error) {
      console.error('Response with image generation failed:', error);
      throw new Error(`Response with image generation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Generate image from text description optimized for social media
   */
  async generateSocialMediaImage(
    description: string,
    options: {
      platform?: 'twitter' | 'instagram' | 'linkedin';
      style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
      includeText?: string;
      aspectRatio?: 'square' | 'landscape' | 'portrait';
    } = {}
  ): Promise<ImageGenerationResponse> {
    // Optimize prompt for social media
    let optimizedPrompt = description;
    
    // Add style modifiers
    if (options.style) {
      const styleModifiers = {
        minimal: 'clean, minimal design, simple composition, white background',
        vibrant: 'vibrant colors, energetic, dynamic composition, high contrast',
        professional: 'professional, clean, business-appropriate, sophisticated',
        artistic: 'artistic, creative, unique perspective, visually striking',
      };
      optimizedPrompt += `, ${styleModifiers[options.style]}`;
    }

    // Add platform-specific optimizations
    if (options.platform) {
      const platformOptimizations = {
        twitter: 'social media friendly, eye-catching, clear focal point',
        instagram: 'Instagram-style, aesthetic, visually appealing, shareable',
        linkedin: 'professional networking, business context, informative',
      };
      optimizedPrompt += `, ${platformOptimizations[options.platform]}`;
    }

    // Add text overlay instruction if needed
    if (options.includeText) {
      optimizedPrompt += `, include text overlay: "${options.includeText}"`;
    }

    // Determine size based on aspect ratio and platform
    let size: '1024x1024' | '1792x1024' | '1024x1792' = '1024x1024';
    if (options.aspectRatio === 'landscape' || options.platform === 'twitter') {
      size = '1792x1024';
    } else if (options.aspectRatio === 'portrait') {
      size = '1024x1792';
    }

    const defaultModel = (process.env.OPENAI_DEFAULT_IMAGE_MODEL || 'dall-e-3') as 'dall-e-2' | 'dall-e-3';
    return this.generateImage(optimizedPrompt, {
      model: defaultModel,
      size,
      quality: 'hd',
      style: 'vivid',
      responseFormat: 'url',
    });
  }

  /**
   * Test the connection to OpenAI
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: process.env.OPENAI_TEST_MODEL || 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Say "OK" if you can hear me.' }],
        max_tokens: 10,
        temperature: 0,
      });
      
      return response.choices[0]?.message?.content?.toLowerCase().includes('ok') || false;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }
}

/**
 * Initialize OpenAI client with environment variables
 */
export function createOpenAIClient(): OpenAIClient {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    throw new Error(
      'OPENAI_API_KEY environment variable is required. ' +
      'Get your API key from https://platform.openai.com/api-keys'
    );
  }

  return new OpenAIClient({
    apiKey,
  });
}

/**
 * Singleton instance for use across the application
 */
let openAIClient: OpenAIClient | null = null;

export function getOpenAIClient(): OpenAIClient {
  if (!openAIClient) {
    openAIClient = createOpenAIClient();
  }
  return openAIClient;
}