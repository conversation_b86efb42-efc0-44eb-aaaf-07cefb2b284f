/**
 * 🚀 INTELLIGENT BATCHING SYSTEM
 *
 * Advanced batching logic to minimize API calls and bandwidth usage
 * while maintaining responsiveness for critical operations
 */
import { Generic<PERSON>atabaseReader, GenericDatabaseWriter } from "convex/server";
/**
 * Batching configuration for different operation types
 */
export declare const BATCH_CONFIG: {
    readonly MENTION_MONITORING: {
        readonly maxBatchSize: 20;
        readonly optimalBatchSize: 8;
        readonly maxWaitTime: number;
        readonly minBatchSize: 3;
        readonly priorityThreshold: 0.8;
    };
    readonly TWEET_SCRAPING: {
        readonly maxBatchSize: 15;
        readonly optimalBatchSize: 5;
        readonly maxWaitTime: number;
        readonly minBatchSize: 2;
        readonly priorityThreshold: 0.7;
    };
    readonly AI_ANALYSIS: {
        readonly maxBatchSize: 50;
        readonly optimalBatchSize: 25;
        readonly maxWaitTime: number;
        readonly minBatchSize: 10;
        readonly priorityThreshold: 0.9;
    };
    readonly HEALTH_CHECKS: {
        readonly maxBatchSize: 100;
        readonly optimalBatchSize: 50;
        readonly maxWaitTime: number;
        readonly minBatchSize: 20;
        readonly priorityThreshold: 0.5;
    };
};
/**
 * Activity-based frequency calculator
 */
export declare class ActivityBasedScheduler {
    /**
     * Calculate optimal cron frequency based on user activity levels
     */
    static calculateOptimalFrequency(operationType: keyof typeof BATCH_CONFIG, activityLevel: "low" | "medium" | "high" | "peak", timeOfDay: number): {
        intervalMinutes: number;
        batchSize: number;
        shouldSkip: boolean;
    };
    /**
     * Determine current activity level based on recent user interactions
     */
    static determineActivityLevel(db: GenericDatabaseReader<any>): Promise<"low" | "medium" | "high" | "peak">;
}
/**
 * Smart batch processor with priority queue
 */
export declare class SmartBatchProcessor {
    private db;
    constructor(db: GenericDatabaseReader<any> | GenericDatabaseWriter<any>);
    /**
     * Create optimized batches based on priority and timing
     */
    createOptimizedBatches<T extends {
        priority?: string;
        lastProcessed?: number;
    }>(items: T[], batchConfig: typeof BATCH_CONFIG[keyof typeof BATCH_CONFIG], currentTime?: number): Promise<{
        priorityBatch: T[];
        regularBatch: T[];
        deferredItems: T[];
        totalBatches: number;
    }>;
    /**
     * Execute batch with rate limiting and error handling
     */
    executeBatchWithRateLimit<T, R>(batch: T[], processor: (item: T) => Promise<R>, rateLimit?: {
        maxConcurrent: number;
        delayBetweenItems: number;
    }): Promise<{
        results: R[];
        errors: {
            item: T;
            error: string;
        }[];
        executionTime: number;
    }>;
    /**
     * Utility: Split array into chunks
     */
    private chunkArray;
    /**
     * Utility: Async delay
     */
    private delay;
}
/**
 * Bandwidth usage calculator for optimization decisions
 */
export declare class BandwidthCalculator {
    /**
     * Estimate bandwidth usage for different batching strategies
     */
    static estimateBandwidthUsage(operationType: keyof typeof BATCH_CONFIG, frequency: number, // executions per day
    batchSize: number, averageResponseSize: number): {
        dailyBandwidth: number;
        monthlyBandwidth: number;
        costEstimate: number;
    };
    /**
     * Compare bandwidth usage between different strategies
     */
    static compareBandwidthStrategies(strategies: Array<{
        name: string;
        frequency: number;
        batchSize: number;
        averageResponseSize: number;
    }>): Array<{
        name: string;
        dailyBandwidth: number;
        monthlyBandwidth: number;
        costEstimate: number;
        efficiencyRatio: number;
    }>;
}
