/**
 * Cache AI response with content-based key
 */
export declare const cacheAIResponse: any;
/**
 * Get cached AI response by content hash
 */
export declare const getCachedAIResponse: any;
/**
 * Increment hit count for cache entry
 */
export declare const incrementHitCount: any;
/**
 * Helper function to generate content hash for AI caching
 */
export declare function generateContentHash(content: string, additionalContext?: string): string;
/**
 * Clean up expired cache entries
 */
export declare const cleanupExpiredCache: any;
/**
 * Get cache statistics
 */
export declare const getCacheStats: any;
/**
 * Wrapper function for AI calls with automatic caching
 */
export declare function withAICache<T>(ctx: any, content: string, responseType: "sentiment_analysis" | "viral_detection" | "content_analysis" | "response_generation", aiFunction: () => Promise<T>, options: {
    model: string;
    additionalContext?: string;
    ttl?: number;
    tokensUsed?: number;
    cost?: number;
}): Promise<T>;
