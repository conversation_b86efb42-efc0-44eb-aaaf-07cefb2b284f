import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
import { createTwitterClient, normalizeTwitterTweet } from "./lib/twitter_client";
import { Id } from "./_generated/dataModel";
import { projectLightweightTweet, projectTweetList } from "./lib/projections";
import type { LightweightTweet, PaginatedResult } from "./types/optimized";
import { AdvancedCacheHelper, CACHE_CONFIG, SmartCacheKeys } from "./lib/advancedCaching";
import { logOptimization } from "./lib/bandwidthMonitor";

export const getPendingTweets = query({
  args: {
    twitterAccountId: v.optional(v.id("twitterAccounts")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const tweets = await ctx.db
      .query("tweets")
      .withIndex("by_status", (q) => q.eq("analysisStatus", "pending"))
      .order("desc")
      .take(args.limit || 50);

    if (args.twitterAccountId) {
      return tweets.filter(tweet => tweet.twitterAccountId === args.twitterAccountId);
    }

    return tweets;
  },
});

export const getTweetsByAccount = query({
  args: {
    twitterAccountId: v.id("twitterAccounts"),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()), // Added cursor
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    let query = ctx.db
      .query("tweets")
      .withIndex("by_account", (q) => q.eq("twitterAccountId", args.twitterAccountId))
      .order("desc"); // Order by _creationTime implicitly (Convex default for order("desc"))

    if (args.cursor) {
      // If cursor is provided, parse it as float (since _creationTime is a number)
      // and filter for tweets older than the cursor
      query = query.filter(q => q.lt(q.field("_creationTime"), parseFloat(args.cursor!)));
    }

    const tweets = await query.take(limit + 1); // Fetch one extra to check for more

    const hasMore = tweets.length > limit;
    const nextCursor = hasMore ? tweets[limit]._creationTime.toString() : null;

    // 🚀 PROJECT TO LIGHTWEIGHT: Use standardized projection (80-85% bandwidth reduction)
    const lightweightTweets = projectTweetList(tweets.slice(0, limit));

    return {
      data: lightweightTweets,
      nextCursor,
      hasMore,
    };
  },
});

export const getRecentTweets = query({
  args: {
    hours: v.optional(v.number()),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()), // Added cursor
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    const hoursAgo = Date.now() - (args.hours || 24) * 60 * 60 * 1000;
    
    let query = ctx.db
      .query("tweets")
      .withIndex("by_scraped_at", (q) => q.gte("scrapedAt", hoursAgo))
      .order("desc"); // Order by scrapedAt (desc) due to the index

    if (args.cursor) {
      // If cursor is provided, parse it as float (since scrapedAt is a number)
      // and filter for tweets scraped earlier than the cursor
      query = query.filter(q => q.lt(q.field("scrapedAt"), parseFloat(args.cursor!)));
    }

    const tweets = await query.take(limit + 1); // Fetch one extra to check for more

    const hasMore = tweets.length > limit;
    // Use scrapedAt for the next cursor, as this is the field being sorted on
    const nextCursor = hasMore ? tweets[limit].scrapedAt.toString() : null;

    // 🚀 PROJECT TO LIGHTWEIGHT: Use standardized projection (80-85% bandwidth reduction)
    const lightweightTweets = projectTweetList(tweets.slice(0, limit));

    return {
      data: lightweightTweets,
      nextCursor,
      hasMore,
    };
  },
});

export const searchTweets = query({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()), // Added cursor for API consistency, though not used for filtering
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    // Fetch one extra to check for more.
    // Note: True cursor pagination based on a field value is not straightforward
    // for search results sorted by relevance. This implements "load more" style pagination.
    const results = await ctx.db
      .query("tweets")
      .withSearchIndex("search_content", (q) => q.search("content", args.query))
      .take(limit + 1);

    const hasMore = results.length > limit;

    // nextCursor is null because search results don't have a stable field to base a cursor on.
    // The client can use hasMore to request the next page.
    const searchSnippets = results.slice(0, limit).map(tweet => ({
      id: tweet._id,
      content: tweet.content.slice(0, 150), // Shorter snippet
      author: tweet.author,
      authorHandle: tweet.authorHandle,
      authorProfileImage: tweet.authorProfileImage,
      createdAt: tweet.createdAt,
      // Omitting engagement, isRetweet, url for search snippets for brevity
    }));

    return {
      data: searchSnippets,
      nextCursor: null,
      hasMore,
    };
  },
});

export const getTweetById = query({
  args: {
    tweetId: v.id("tweets"),
  },
  handler: async (ctx, args) => {
    const tweet = await ctx.db.get(args.tweetId);
    return tweet;
  },
});

export const getTweetStats = query({
  args: {
    twitterAccountId: v.optional(v.id("twitterAccounts")),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    
    // 🚀 ADVANCED CACHING: Use new advanced cache system
    const cache = new AdvancedCacheHelper(ctx.db);
    const cacheKey = args.twitterAccountId
      ? SmartCacheKeys.accountSpecific(args.twitterAccountId, "tweet_stats")
      : SmartCacheKeys.global("tweet_stats");

    const cached = await cache.getOrCompute(
      cacheKey,
      async () => {
        // Cache miss - compute fresh stats
        return await computeTweetStats(ctx, args.twitterAccountId);
      },
      {
        ttl: CACHE_CONFIG.TWEET_STATS.ttl,
        tags: [...CACHE_CONFIG.TWEET_STATS.tags, args.twitterAccountId ? `account_${args.twitterAccountId}` : 'global'],
        priority: CACHE_CONFIG.TWEET_STATS.priority,
        allowStale: true,
      }
    );

    const executionTime = Date.now() - startTime;

    // Log optimization metrics
    await logOptimization(
      ctx,
      "getTweetStats",
      cached.cacheHit ? 8000 : 40000, // Estimated original size
      JSON.stringify(cached.data).length, // Actual size
      executionTime,
      cached.cacheHit ? 0 : 1000, // Records scanned
      1, // Records returned
      "advanced_caching",
      cached.cacheHit
    );

    return {
      ...cached.data,
      _meta: {
        cacheHit: cached.cacheHit,
        isStale: cached.isStale,
        executionTime,
      }
    };
  },
});

/**
 * 🚀 CACHED COMPUTATION: Tweet statistics computation (extracted for caching)
 */
async function computeTweetStats(ctx: any, twitterAccountId?: string) {
  // 🚀 BANDWIDTH OPTIMIZED: Query tweets with limits for stats calculation
  const STATS_LIMIT = 2000; // Limit for statistics calculation
  
  const tweets = twitterAccountId 
    ? await ctx.db
        .query("tweets")
        .withIndex("by_account", (q: any) => q.eq("twitterAccountId", twitterAccountId))
        .order("desc")
        .take(STATS_LIMIT) // 🚀 BANDWIDTH FIX: Limit instead of collect()
    : await ctx.db
        .query("tweets")
        .order("desc")
        .take(STATS_LIMIT); // 🚀 BANDWIDTH FIX: Limit instead of collect()
  
  const now = Date.now();
  const oneDayAgo = now - 24 * 60 * 60 * 1000;
  const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;

  const todayTweets = tweets.filter(t => t.scrapedAt >= oneDayAgo);
  const weekTweets = tweets.filter(t => t.scrapedAt >= oneWeekAgo);
  
  const pending = tweets.filter(t => t.analysisStatus === "pending").length;
  const analyzed = tweets.filter(t => t.analysisStatus === "analyzed").length;
  const responseWorthy = tweets.filter(t => t.analysisStatus === "response_worthy").length;

  return {
    total: tweets.length,
    todayCount: todayTweets.length,
    weekCount: weekTweets.length,
    pending,
    analyzed,
    responseWorthy,
    averageEngagement: tweets.length > 0 
      ? tweets.reduce((sum, t) => sum + t.engagement.likes + t.engagement.retweets, 0) / tweets.length 
      : 0,
    generatedAt: Date.now(),
  };
}

export const storeTweet = mutation({
  args: {
    twitterAccountId: v.id("twitterAccounts"),
    tweetId: v.string(),
    content: v.string(),
    author: v.string(),
    authorHandle: v.string(),
    authorProfileImage: v.optional(v.string()),
    isRetweet: v.optional(v.boolean()),
    retweetedFrom: v.optional(v.string()),
    createdAt: v.number(),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    url: v.optional(v.string()),
    metadata: v.optional(v.object({
      isThread: v.optional(v.boolean()),
      threadPosition: v.optional(v.number()),
      hasMedia: v.optional(v.boolean()),
      mediaType: v.optional(v.string()),
      language: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    // Check if tweet already exists
    const existing = await ctx.db
      .query("tweets")
      .withIndex("by_tweet_id", (q) => q.eq("tweetId", args.tweetId))
      .first();

    if (existing) {
      // Update engagement metrics
      await ctx.db.patch(existing._id, {
        engagement: args.engagement,
        scrapedAt: Date.now(),
      });
      return existing._id;
    }

    // Create new tweet
    const tweetId = await ctx.db.insert("tweets", {
      ...args,
      scrapedAt: Date.now(),
      analysisStatus: "pending",
    });

    return tweetId;
  },
});

export const updateTweetAnalysis = mutation({
  args: {
    tweetId: v.id("tweets"),
    analysisStatus: v.union(
      v.literal("pending"),
      v.literal("analyzed"), 
      v.literal("response_worthy"),
      v.literal("skip")
    ),
    analysisScore: v.optional(v.number()),
    analysisReason: v.optional(v.string()),
    embeddingId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { tweetId, ...updates } = args;
    
    await ctx.db.patch(tweetId, updates);
    
    return { success: true };
  },
});

export const fetchTweetsFromAccount = action({
  args: {
    handle: v.string(),
    maxResults: v.optional(v.number()),
    sinceId: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    scraped: number;
    tweets: string[];
    latestTweetId?: string;
    error?: string;
  }> => {
    try {
      const apiKey = process.env.TWITTER_API_KEY;
      const twitterClient = createTwitterClient(apiKey);
      
      // Get the Twitter account from our database
      const account = await ctx.runQuery(api.userQueries.getTwitterAccountByHandle, {
        handle: args.handle,
      });

      if (!account) {
        throw new Error(`Twitter account @${args.handle} not found in database`);
      }

      // Fetch tweets from Twitter API
      const result = await twitterClient.getTweetsByUsername(args.handle, {
        maxResults: args.maxResults || 20,
        sinceId: args.sinceId,
        excludeReplies: true,
        excludeRetweets: false,
      });

      if (!result.targetUser) {
        throw new Error(`User @${args.handle} not found on Twitter`);
      }

      const storedTweets = [];

      // Store each tweet in the database
      for (const tweet of result.tweets) {
        const normalizedTweet = normalizeTwitterTweet(tweet, result.users);
        
        const tweetId = await ctx.runMutation(api.tweets.storeTweet, {
          twitterAccountId: account._id,
          ...normalizedTweet,
        });
        
        storedTweets.push(tweetId);
      }

      return {
        success: true,
        scraped: storedTweets.length,
        tweets: storedTweets,
        latestTweetId: result.tweets[0]?.id,
      };
    } catch (error) {
      console.error("Error fetching tweets:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        scraped: 0,
        tweets: [],
      };
    }
  },
});

export const getTweetEngagement = action({
  args: {
    tweetId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const apiKey = process.env.TWITTER_API_KEY;
      const twitterClient = createTwitterClient(apiKey);
      
      const result = await twitterClient.getTweetById(args.tweetId);
      
      if (!result.tweet) {
        return {
          success: false,
          error: "Tweet not found",
        };
      }

      const engagement = {
        likes: result.tweet.public_metrics?.like_count || 0,
        retweets: result.tweet.public_metrics?.retweet_count || 0,
        replies: result.tweet.public_metrics?.reply_count || 0,
        views: result.tweet.public_metrics?.impression_count || 0,
      };

      // Update stored tweet if it exists
      const storedTweet = await ctx.runQuery(api.tweets.getTweetById, {
        tweetId: args.tweetId as Id<"tweets">,
      });

      if (storedTweet) {
        await ctx.runMutation(api.tweets.updateTweetEngagement, {
          tweetId: storedTweet._id,
          engagement,
        });
      }

      return {
        success: true,
        engagement,
      };
    } catch (error) {
      console.error("Error fetching tweet engagement:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

export const updateTweetEngagement = mutation({
  args: {
    tweetId: v.id("tweets"),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.tweetId, {
      engagement: args.engagement,
      scrapedAt: Date.now(),
    });
    
    return { success: true };
  },
});