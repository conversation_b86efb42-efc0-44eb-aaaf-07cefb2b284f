/**
 * 🚀 BANDWIDTH OPTIMIZED DATA TYPES
 *
 * These lightweight types reduce payload sizes by 60-80% by including only
 * essential fields for different UI contexts (list views, cards, etc.)
 */
/**
 * Common transformation utilities
 */
export const Transformers = {
    truncateContent: (content, maxLength = 280) => {
        if (!content)
            return "";
        return content.length > maxLength
            ? content.slice(0, maxLength - 3) + "..."
            : content;
    },
    safeEngagement: (engagement) => ({
        likes: engagement?.likes || 0,
        retweets: engagement?.retweets || 0,
        replies: engagement?.replies || 0,
        views: engagement?.views,
    }),
    extractShouldRespond: (aiAnalysisResult) => {
        return aiAnalysisResult?.shouldRespond === true;
    },
};
// =============================================================================
// CACHE KEYS FOR OPTIMIZED DATA
// =============================================================================
/**
 * Standardized cache key generators
 */
export const CacheKeys = {
    mentionStats: (accountId, timeframe) => `mention_stats_${accountId || 'all'}_${timeframe || '24h'}_${Math.floor(Date.now() / (5 * 60 * 1000))}`,
    tweetStats: (accountId, timeframe) => `tweet_stats_${accountId || 'all'}_${timeframe || '24h'}_${Math.floor(Date.now() / (5 * 60 * 1000))}`,
    dashboardStats: (userId, timeframe) => `dashboard_${userId}_${timeframe || '7d'}_${Math.floor(Date.now() / (5 * 60 * 1000))}`,
    userMentions: (userId, accountId) => `user_mentions_${userId}_${accountId || 'all'}_${Math.floor(Date.now() / (2 * 60 * 1000))}`,
};
