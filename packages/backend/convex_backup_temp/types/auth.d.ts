/**
 * Authentication and user-related type definitions
 */
import { Id } from "../_generated/dataModel";
export interface UserIdentity {
    subject: string;
    name?: string;
    email?: string;
}
export interface AuthContext {
    auth: {
        getUserIdentity(): Promise<UserIdentity | null>;
    };
    db: {
        query(table: string): any;
        insert(table: string, document: any): Promise<Id<any>>;
        patch(id: Id<any>, fields: any): Promise<void>;
    };
}
export interface UserDoc {
    _id: Id<"users">;
    name: string;
    email: string;
    clerkId: string;
    image?: string;
    primaryWalletId?: Id<"wallets">;
    createdAt: number;
    updatedAt?: number;
}
export declare function getUserId(ctx: AuthContext): Promise<Id<"users"> | null>;
