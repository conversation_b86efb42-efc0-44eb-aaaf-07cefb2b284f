/**
 * 🚀 BANDWIDTH OPTIMIZED DATA TYPES
 *
 * These lightweight types reduce payload sizes by 60-80% by including only
 * essential fields for different UI contexts (list views, cards, etc.)
 */
import type { Id } from "../_generated/dataModel";
/**
 * Lightweight mention for list views and cards
 * Reduces ~2-5KB full mention to ~300-500 bytes (80-90% reduction)
 */
export interface LightweightMention {
    _id: Id<"mentions">;
    content: string;
    priority: "high" | "medium" | "low";
    authorHandle: string;
    authorName: string;
    authorProfileImage?: string;
    createdAt: number;
    discoveredAt: number;
    engagement: {
        likes: number;
        retweets: number;
        replies: number;
    };
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    isProcessed: boolean;
    hasResponse: boolean;
    responseCount: number;
}
/**
 * Ultra-lightweight mention for dashboard stats and counters
 * Only essential fields for calculations (~100-150 bytes)
 */
export interface MentionSummary {
    _id: Id<"mentions">;
    priority: "high" | "medium" | "low";
    mentionType: "mention" | "reply" | "quote" | "retweet_with_comment";
    discoveredAt: number;
    isProcessed: boolean;
    isNotificationSent: boolean;
    shouldRespond?: boolean;
}
/**
 * Lightweight tweet for list views and feeds
 * Reduces ~1-3KB full tweet to ~200-400 bytes (70-85% reduction)
 */
export interface LightweightTweet {
    _id: Id<"tweets">;
    content: string;
    author: string;
    authorHandle: string;
    authorProfileImage?: string;
    createdAt: number;
    scrapedAt: number;
    engagement: {
        likes: number;
        retweets: number;
        replies: number;
        views?: number;
    };
    isRetweet: boolean;
    retweetedFrom?: string;
    url: string;
    analysisStatus: "pending" | "analyzing" | "analyzed" | "response_worthy" | "not_worthy";
}
/**
 * Ultra-lightweight tweet for dashboard stats
 * Only fields needed for metrics (~80-100 bytes)
 */
export interface TweetSummary {
    _id: Id<"tweets">;
    scrapedAt: number;
    analysisStatus: "pending" | "analyzing" | "analyzed" | "response_worthy" | "not_worthy";
    engagement: {
        likes: number;
        retweets: number;
        replies: number;
    };
}
/**
 * Lightweight response for queues and management views
 * Reduces ~1-2KB full response to ~250-350 bytes (70-80% reduction)
 */
export interface LightweightResponse {
    _id: Id<"responses">;
    mentionId: Id<"mentions">;
    targetType: "mention" | "tweet";
    content: string;
    status: "pending" | "approved" | "posted" | "rejected" | "draft";
    aiModel: string;
    confidence: number;
    createdAt: number;
    scheduledAt?: number;
    postedAt?: number;
}
/**
 * Ultra-lightweight response for counters and stats
 */
export interface ResponseSummary {
    _id: Id<"responses">;
    status: "pending" | "approved" | "posted" | "rejected" | "draft";
    createdAt: number;
    confidence: number;
}
/**
 * Standardized pagination result type
 */
export interface PaginatedResult<T> {
    data: T[];
    nextCursor: string | null;
    hasMore: boolean;
    totalEstimate?: number;
}
/**
 * Pagination arguments
 */
export interface PaginationArgs {
    limit?: number;
    cursor?: string;
}
/**
 * Optimized dashboard stats structure
 */
export interface DashboardStats {
    accounts: {
        total: number;
        active: number;
        monitoring: number;
    };
    tweets: {
        total: number;
        recent: number;
        pending: number;
        analyzed: number;
        responseWorthy: number;
        averageEngagement: number;
    };
    mentions: {
        total: number;
        recent: number;
        unread: number;
        unprocessed: number;
        highPriority: number;
        responseOpportunities: number;
    };
    responses: {
        total: number;
        recent: number;
        draft: number;
        approved: number;
        posted: number;
        averageConfidence: number;
    };
    engagement: {
        totalLikes: number;
        totalRetweets: number;
        totalReplies: number;
        averageEngagementRate: number;
        topPerformingTweet: {
            id: string;
            content: string;
            engagement: {
                likes: number;
                retweets: number;
                replies: number;
            };
            url: string;
        } | null;
    };
    activity: {
        tweetsScraped: number;
        mentionsFound: number;
        responsesGenerated: number;
        lastActivity: number | null;
    };
}
/**
 * Helper type for field projection functions
 */
export type ProjectionFunction<TFull, TLight> = (full: TFull) => TLight;
/**
 * Common transformation utilities
 */
export declare const Transformers: {
    truncateContent: (content: string, maxLength?: number) => string;
    safeEngagement: (engagement: any) => {
        likes: any;
        retweets: any;
        replies: any;
        views: any;
    };
    extractShouldRespond: (aiAnalysisResult: any) => boolean;
};
/**
 * Standardized cache key generators
 */
export declare const CacheKeys: {
    mentionStats: (accountId?: string, timeframe?: string) => string;
    tweetStats: (accountId?: string, timeframe?: string) => string;
    dashboardStats: (userId: string, timeframe?: string) => string;
    userMentions: (userId: string, accountId?: string) => string;
};
