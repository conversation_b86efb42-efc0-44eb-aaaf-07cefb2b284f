import { httpRouter } from "convex/server";
import { api } from "./_generated/api";
import { secureHttpAction } from "./lib/security_headers";
import { clerkBillingWebhook } from "./billing/webhooks";

/**
 * 🌐 BuddyChip Pro HTTP Router
 * 
 * Handles external HTTP requests including:
 * - Webhook endpoints for billing and integrations
 * - Public API endpoints
 * - Health check endpoints
 */

const http = httpRouter();

/**
 * 🔔 Clerk Billing Webhook Endpoint
 * 
 * Receives subscription lifecycle events from Clerk billing system.
 * This endpoint is called by <PERSON> when subscription events occur.
 * 
 * Events handled:
 * - subscription.created
 * - subscription.updated
 * - subscription.canceled
 * - invoice.payment_succeeded
 * - invoice.payment_failed
 * - customer.created
 */
http.route({
  path: "/api/billing/webhook",
  method: "POST",
  handler: clerkBillingWebhook,
});

/**
 * 🏥 Health Check Endpoint
 * 
 * Simple health check for monitoring and load balancers.
 * Does not require authentication.
 */
http.route({
  path: "/health",
  method: "GET",
  handler: secureHttpAction(async (ctx, request) => {
    return new Response(
      JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        service: "buddychip-pro-backend",
        version: "1.0.0",
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }),
});

/**
 * 🔐 Authenticated Health Check
 * 
 * Health check that requires authentication.
 * Useful for testing auth flow and user-specific health.
 */
http.route({
  path: "/health/auth",
  method: "GET", 
  handler: secureHttpAction(async (ctx, request) => {
    // Require authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return new Response(
        JSON.stringify({ error: "Authentication required" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        status: "healthy",
        authenticated: true,
        user: {
          id: identity.subject,
          name: identity.name,
        },
        timestamp: new Date().toISOString(),
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }),
});

/**
 * 🔧 Webhook Test Endpoint
 * 
 * Test endpoint for webhook development and debugging.
 * Only available in development mode.
 */
if (process.env.NODE_ENV !== "production") {
  http.route({
    path: "/api/webhook/test",
    method: "POST",
    handler: secureHttpAction(async (ctx, request) => {
      console.log("🧪 Test webhook received");
      
      const body = await request.text();
      const headers = Object.fromEntries(request.headers.entries());
      
      console.log("Headers:", headers);
      console.log("Body:", body);
      
      return new Response(
        JSON.stringify({
          message: "Test webhook received successfully",
          timestamp: new Date().toISOString(),
          headers,
          body: body.substring(0, 500), // Truncate for safety
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }),
  });
}

/**
 * 📊 API Status Endpoint
 * 
 * Provides API status and basic metrics.
 * Useful for monitoring and debugging.
 */
http.route({
  path: "/api/status",
  method: "GET",
  handler: secureHttpAction(async (ctx, request) => {
    return new Response(
      JSON.stringify({
        api: "BuddyChip Pro Backend",
        version: "1.0.0",
        status: "operational",
        timestamp: new Date().toISOString(),
        endpoints: {
          billing_webhook: "/api/billing/webhook",
          health: "/health",
          auth_health: "/health/auth",
          status: "/api/status",
        },
        environment: process.env.NODE_ENV || "development",
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }),
});

export default http;
